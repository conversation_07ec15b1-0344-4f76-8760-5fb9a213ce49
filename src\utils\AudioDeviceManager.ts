/**
 * 音頻設備管理器
 * 處理音頻輸入設備的枚舉和選擇
 * 注意：此類只能在瀏覽器環境中使用
 */

export interface AudioDevice {
  deviceId: string
  label: string
  kind: string
  groupId: string
}

export class AudioDeviceManager {
  private selectedDeviceId: string | null = null
  private devices: AudioDevice[] = []

  /**
   * 獲取所有可用的音頻輸入設備
   */
  async getAudioInputDevices(): Promise<AudioDevice[]> {
    if (typeof window === 'undefined' || !navigator?.mediaDevices) {
      throw new Error('此功能只能在瀏覽器環境中使用')
    }

    try {
      // 請求權限以獲取設備標籤
      await navigator.mediaDevices.getUserMedia({ audio: true })

      const devices = await navigator.mediaDevices.enumerateDevices()

      this.devices = devices
        .filter((device: MediaDeviceInfo) => device.kind === 'audioinput')
        .map((device: MediaDeviceInfo) => ({
          deviceId: device.deviceId,
          label: device.label || `麥克風 ${device.deviceId.slice(0, 8)}`,
          kind: device.kind,
          groupId: device.groupId
        }))

      return this.devices
    } catch (error) {
      console.error('Failed to enumerate audio devices:', error)
      throw new Error('無法獲取音頻設備列表，請檢查麥克風權限')
    }
  }

  /**
   * 設定選中的音頻設備
   */
  setSelectedDevice(deviceId: string): void {
    this.selectedDeviceId = deviceId
    // 保存到本地存儲
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.setItem('selectedAudioDevice', deviceId)
    }
  }

  /**
   * 獲取選中的音頻設備ID
   */
  getSelectedDeviceId(): string | null {
    if (!this.selectedDeviceId && typeof window !== 'undefined' && window.localStorage) {
      // 從本地存儲讀取
      this.selectedDeviceId = localStorage.getItem('selectedAudioDevice')
    }
    return this.selectedDeviceId
  }

  /**
   * 獲取選中的音頻設備信息
   */
  getSelectedDevice(): AudioDevice | null {
    const deviceId = this.getSelectedDeviceId()
    if (!deviceId) return null
    
    return this.devices.find(device => device.deviceId === deviceId) || null
  }

  /**
   * 獲取音頻流（使用選中的設備）
   */
  async getAudioStream(constraints: MediaStreamConstraints = {}): Promise<MediaStream> {
    if (typeof window === 'undefined' || !navigator?.mediaDevices) {
      throw new Error('此功能只能在瀏覽器環境中使用')
    }

    const deviceId = this.getSelectedDeviceId()

    const audioConstraints: MediaTrackConstraints = {
      sampleRate: 16000,
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true,
      ...constraints.audio as MediaTrackConstraints
    }

    // 如果有選中的設備，使用該設備
    if (deviceId) {
      audioConstraints.deviceId = { exact: deviceId }
    }

    try {
      return await navigator.mediaDevices.getUserMedia({
        audio: audioConstraints,
        video: false
      })
    } catch (error) {
      console.error('Failed to get audio stream:', error)

      // 如果指定設備失敗，嘗試使用預設設備
      if (deviceId) {
        console.warn('Failed to use selected device, falling back to default')
        return await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16000,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          },
          video: false
        })
      }

      throw new Error('無法存取音頻設備，請檢查麥克風權限')
    }
  }

  /**
   * 測試音頻設備
   */
  async testAudioDevice(deviceId: string): Promise<boolean> {
    if (typeof window === 'undefined' || !navigator?.mediaDevices) {
      return false
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: { deviceId: { exact: deviceId } },
        video: false
      })

      // 停止測試流
      stream.getTracks().forEach((track: MediaStreamTrack) => track.stop())
      return true
    } catch (error) {
      console.error('Audio device test failed:', error)
      return false
    }
  }

  /**
   * 監聽設備變化
   */
  onDeviceChange(callback: () => void): void {
    if (typeof window !== 'undefined' && navigator?.mediaDevices) {
      navigator.mediaDevices.addEventListener('devicechange', callback)
    }
  }

  /**
   * 移除設備變化監聽器
   */
  removeDeviceChangeListener(callback: () => void): void {
    if (typeof window !== 'undefined' && navigator?.mediaDevices) {
      navigator.mediaDevices.removeEventListener('devicechange', callback)
    }
  }
}

// 單例實例
export const audioDeviceManager = new AudioDeviceManager()
