/**
 * Electron 主程序入口點
 * 負責應用程式初始化和基本設定
 */

import { app, BrowserWindow } from 'electron'

// 擴展 app 實例以包含 isQuiting 屬性
declare global {
  namespace Electron {
    interface App {
      isQuiting?: boolean
    }
  }
}
import path from 'path'
import { WindowManager } from './managers/WindowManager'
import { TrayManager } from './managers/TrayManager'
import { ShortcutManager } from './managers/ShortcutManager'
import { IPCManager } from './managers/IPCManager'
import { ServiceManager } from './managers/ServiceManager'
import { isDev } from './utils/environment'

class SpeechPilotApp {
  public windowManager: WindowManager
  private trayManager: TrayManager
  private shortcutManager: ShortcutManager
  private ipcManager: IPCManager
  private serviceManager: ServiceManager

  constructor() {
    this.serviceManager = new ServiceManager()
    this.windowManager = new WindowManager()
    this.trayManager = new TrayManager(this.windowManager)
    this.shortcutManager = new ShortcutManager(this.windowManager)
    this.ipcManager = new IPCManager(this.windowManager, this.serviceManager)
  }

  async initialize() {
    try {
      console.log('Initializing SpeechPilot application...')
      
      // 初始化服務
      await this.serviceManager.initialize()
      
      // 創建主視窗（但不顯示）
      this.windowManager.createMainWindow()
      
      // 創建托盤
      this.trayManager.createTray()
      
      // 註冊全域快捷鍵
      this.shortcutManager.registerShortcuts()
      
      // 設定 IPC 事件處理
      this.ipcManager.setupEventHandlers()

      // 設定管理器之間的連接
      this.ipcManager.setRecordingController(this.shortcutManager.getRecordingController())
      this.ipcManager.setShortcutManager(this.shortcutManager)

      console.log('SpeechPilot application initialized successfully')
    } catch (error) {
      console.error('Failed to initialize application:', error)
      throw error
    }
  }

  async shutdown() {
    console.log('Shutting down SpeechPilot application...')
    
    this.shortcutManager.unregisterAll()
    this.trayManager.destroy()
    await this.serviceManager.shutdown()
    
    console.log('Application shutdown complete')
  }
}

// 應用程式實例
let speechPilotApp: SpeechPilotApp | null = null

// 應用程式就緒時初始化
app.whenReady().then(async () => {
  try {
    speechPilotApp = new SpeechPilotApp()
    await speechPilotApp.initialize()
  } catch (error) {
    console.error('Failed to start application:', error)
    app.quit()
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      speechPilotApp?.windowManager.createMainWindow()
    }
  })
})

// 所有視窗關閉時的處理
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 應用程式即將退出時的清理
app.on('will-quit', async () => {
  if (speechPilotApp) {
    await speechPilotApp.shutdown()
  }
})

app.on('before-quit', () => {
  app.isQuiting = true
})
