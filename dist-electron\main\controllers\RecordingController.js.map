{"version": 3, "file": "RecordingController.js", "sourceRoot": "", "sources": ["../../../src/main/controllers/RecordingController.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAMH,MAAa,mBAAmB;IAK9B,YAAY,aAA4B;QAHhC,gBAAW,GAAG,KAAK,CAAA;QACnB,gBAAW,GAAyB,IAAI,CAAA;QAG9C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;IACpC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAmB;QAClC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,eAAe;YACf,IAAI,CAAC,aAAa,EAAE,CAAA;QACtB,CAAC;aAAM,CAAC;YACN,eAAe;YACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAmB;QAChC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,OAAO,CAAC,CAAA;QAEjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QAEvB,SAAS;QACT,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAA;QAEhE,qBAAqB;QACrB,IAAI,eAAe,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC;YAC5C,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE;gBACvD,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAA;gBACrE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,OAAO,CAAC,CAAA;YAClD,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAA;YACrE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,OAAO,CAAC,CAAA;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAM;QAE7B,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAA;QAElE,gBAAgB;QAChB,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAA;IAC5D,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,WAAW,OAAO,CAAC,CAAA;YAE7D,OAAO;YACP,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YAExB,SAAS;YACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;QAE1C,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAA;IAC3C,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,iBAAiB;QACjB,eAAe;QACf,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;QAEzC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QAEvB,qBAAqB;QACrB,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAA;QAC3C,CAAC,EAAE,IAAI,CAAC,CAAA;IACV,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;QAEtC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QACxB,sBAAsB;IACxB,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAA;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;CACF;AA7ID,kDA6IC"}