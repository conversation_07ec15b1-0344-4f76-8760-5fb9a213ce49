.recording-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.recording-popup {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(32px);
  border-radius: 28px;
  padding: 2.5rem;
  text-align: center;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  min-width: 360px;
  max-width: 460px;
  position: relative;
  -webkit-app-region: drag; /* 使整個視窗可拖拽 */
  user-select: none;
  overflow: hidden;
}

.recording-popup.ai-mode {
  background:
    linear-gradient(135deg, rgba(102, 126, 234, 0.25), rgba(118, 75, 162, 0.25)),
    rgba(255, 255, 255, 0.08);
  border-color: rgba(102, 126, 234, 0.4);
}

.recording-popup.ai-mode::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 28px;
  z-index: -1;
}

.recording-popup.direct-mode {
  background:
    linear-gradient(135deg, rgba(34, 193, 195, 0.25), rgba(253, 187, 45, 0.25)),
    rgba(255, 255, 255, 0.08);
  border-color: rgba(34, 193, 195, 0.4);
}

.recording-popup.direct-mode::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(34, 193, 195, 0.1), rgba(253, 187, 45, 0.1));
  border-radius: 28px;
  z-index: -1;
}

.recording-content {
  margin-bottom: 1.5rem;
  -webkit-app-region: no-drag; /* 內容區域不可拖拽，避免影響按鈕點擊 */
}

/* 關閉按鈕 */
.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;
  font-size: 16px;
  line-height: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.close-button:hover {
  background: rgba(255, 107, 107, 0.4);
  border-color: rgba(255, 107, 107, 0.6);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
}

.close-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
}

.recording-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 96px;
  height: 96px;
  border-radius: 50%;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05)),
    rgba(255, 255, 255, 0.1);
  margin: 0 auto 1.5rem auto;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.recording-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.recording-popup h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.recording-info {
  margin-bottom: 1rem;
}

.duration {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-family: 'Courier New', monospace;
}

.hint {
  font-size: 0.9rem;
  opacity: 0.8;
}

.processing-info {
  margin: 1rem 0;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  margin: 1rem 0;
}

.complete-info p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.error-info {
  margin: 1rem 0;
  text-align: center;
}

.error-info p {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #ff6b6b;
  opacity: 0.9;
}

.retry-btn {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.4);
  border-radius: 12px;
  padding: 0.8rem 1.5rem;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  -webkit-app-region: no-drag;
  outline: none;
}

.retry-btn:hover {
  background: rgba(255, 107, 107, 0.3);
  border-color: rgba(255, 107, 107, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.2);
}

.retry-btn:active {
  transform: translateY(0);
}

.stop-btn {
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05)),
    rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 auto;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.stop-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.stop-btn:hover {
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1)),
    rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stop-btn:hover::before {
  left: 100%;
}

.stop-btn:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
