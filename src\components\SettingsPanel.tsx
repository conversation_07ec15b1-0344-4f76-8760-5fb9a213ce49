import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Save, Keyboard, Mic, RotateCcw } from 'lucide-react'
import { audioDeviceManager, AudioDevice } from '../utils/AudioDeviceManager'
import '../styles/components/SettingsPanel.css'

interface SettingsPanelProps {
  hotkeys: {
    aiMode: string
    directMode: string
  }
  onSave: (settings: {
    hotkeys: { aiMode: string; directMode: string }
    apiKeys: {
      azureSpeechKey: string
      azureSpeechRegion: string
      azureOpenAIKey: string
      azureOpenAIEndpoint: string
    }
  }) => void
  onClose: () => void
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({ hotkeys, onSave, onClose }) => {
  const [tempHotkeys, setTempHotkeys] = useState(hotkeys)
  const [isRecording, setIsRecording] = useState<'aiMode' | 'directMode' | null>(null)
  const [apiKeys, setApiKeys] = useState({
    azureSpeechKey: '',
    azureSpeechRegion: 'eastus',
    azureOpenAIKey: '',
    azureOpenAIEndpoint: ''
  })
  const [showApiKeys, setShowApiKeys] = useState(false)
  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([])
  const [selectedAudioDevice, setSelectedAudioDevice] = useState<string>('')
  const [isLoadingDevices, setIsLoadingDevices] = useState(false)

  // 載入音頻設備
  const loadAudioDevices = async () => {
    setIsLoadingDevices(true)
    try {
      const devices = await audioDeviceManager.getAudioInputDevices()
      setAudioDevices(devices)

      // 設定當前選中的設備
      const currentDevice = audioDeviceManager.getSelectedDeviceId()
      if (currentDevice) {
        setSelectedAudioDevice(currentDevice)
      } else if (devices.length > 0) {
        // 如果沒有選中的設備，預設選擇第一個
        setSelectedAudioDevice(devices[0].deviceId)
        audioDeviceManager.setSelectedDevice(devices[0].deviceId)
      }
    } catch (error) {
      console.error('Failed to load audio devices:', error)
    } finally {
      setIsLoadingDevices(false)
    }
  }

  // 處理音頻設備選擇
  const handleAudioDeviceChange = (deviceId: string) => {
    setSelectedAudioDevice(deviceId)
    audioDeviceManager.setSelectedDevice(deviceId)
  }

  // 組件載入時獲取音頻設備
  useEffect(() => {
    loadAudioDevices()
  }, [])

  const handleKeyRecord = (mode: 'aiMode' | 'directMode') => {
    setIsRecording(mode)
    
    const handleKeyDown = (e: KeyboardEvent) => {
      e.preventDefault()
      
      const keys = []
      if (e.ctrlKey) keys.push('Ctrl')
      if (e.shiftKey) keys.push('Shift')
      if (e.altKey) keys.push('Alt')
      if (e.metaKey) keys.push('Meta')
      
      if (e.key !== 'Control' && e.key !== 'Shift' && e.key !== 'Alt' && e.key !== 'Meta') {
        keys.push(e.key.toUpperCase())
      }
      
      if (keys.length > 1) {
        const hotkeyString = keys.join('+')
        setTempHotkeys(prev => ({
          ...prev,
          [mode]: hotkeyString
        }))
        setIsRecording(null)
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
    
    document.addEventListener('keydown', handleKeyDown)
    
    // 5秒後自動取消錄製
    setTimeout(() => {
      setIsRecording(null)
      document.removeEventListener('keydown', handleKeyDown)
    }, 5000)
  }

  const handleSave = () => {
    onSave({
      hotkeys: tempHotkeys,
      apiKeys: apiKeys
    })
    onClose()
  }

  const handleCancel = () => {
    setTempHotkeys(hotkeys)
    onClose()
  }

  return (
    <AnimatePresence>
      <motion.div
        className="settings-overlay"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <motion.div
          className="settings-panel"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          <div className="settings-header">
            <h2>設定</h2>
            <button className="close-btn" onClick={handleCancel}>
              <X size={20} />
            </button>
          </div>

          <div className="settings-content">
            <div className="setting-group">
              <h3>快捷鍵設定</h3>
              
              <div className="hotkey-setting">
                <label>AI 語音助手模式</label>
                <div className="hotkey-input">
                  <input
                    type="text"
                    value={tempHotkeys.aiMode}
                    readOnly
                    placeholder="點擊錄製按鈕設定快捷鍵"
                  />
                  <button
                    className={`record-btn ${isRecording === 'aiMode' ? 'recording' : ''}`}
                    onClick={() => handleKeyRecord('aiMode')}
                    disabled={isRecording !== null}
                  >
                    <Keyboard size={16} />
                    {isRecording === 'aiMode' ? '按下快捷鍵...' : '錄製'}
                  </button>
                </div>
              </div>

              <div className="hotkey-setting">
                <label>直接語音轉文字模式</label>
                <div className="hotkey-input">
                  <input
                    type="text"
                    value={tempHotkeys.directMode}
                    readOnly
                    placeholder="點擊錄製按鈕設定快捷鍵"
                  />
                  <button
                    className={`record-btn ${isRecording === 'directMode' ? 'recording' : ''}`}
                    onClick={() => handleKeyRecord('directMode')}
                    disabled={isRecording !== null}
                  >
                    <Keyboard size={16} />
                    {isRecording === 'directMode' ? '按下快捷鍵...' : '錄製'}
                  </button>
                </div>
              </div>
            </div>

            <div className="setting-group">
              <h3>音頻設備</h3>

              <div className="audio-device-setting">
                <label>麥克風設備</label>
                <div className="device-selector">
                  <select
                    value={selectedAudioDevice}
                    onChange={(e) => handleAudioDeviceChange(e.target.value)}
                    disabled={isLoadingDevices}
                  >
                    {isLoadingDevices ? (
                      <option value="">載入中...</option>
                    ) : audioDevices.length === 0 ? (
                      <option value="">未找到音頻設備</option>
                    ) : (
                      audioDevices.map(device => (
                        <option key={device.deviceId} value={device.deviceId}>
                          {device.label}
                        </option>
                      ))
                    )}
                  </select>
                  <button
                    className="refresh-btn"
                    onClick={loadAudioDevices}
                    disabled={isLoadingDevices}
                    title="重新載入設備"
                  >
                    <RotateCcw size={16} />
                  </button>
                </div>
                <p className="device-hint">
                  <Mic size={14} />
                  選擇用於語音輸入的麥克風設備
                </p>
              </div>
            </div>

            <div className="setting-group">
              <h3>API 配置</h3>

              <div className="api-toggle">
                <button
                  className="toggle-btn"
                  onClick={() => setShowApiKeys(!showApiKeys)}
                >
                  {showApiKeys ? '隱藏' : '顯示'} API 金鑰設定
                </button>
              </div>

              {showApiKeys && (
                <div className="api-settings">
                  <div className="api-setting">
                    <label>Azure Speech Service API Key</label>
                    <input
                      type="password"
                      value={apiKeys.azureSpeechKey}
                      onChange={(e) => setApiKeys(prev => ({
                        ...prev,
                        azureSpeechKey: e.target.value
                      }))}
                      placeholder="輸入 Azure Speech Service API Key"
                    />
                  </div>

                  <div className="api-setting">
                    <label>Azure Speech Service Region</label>
                    <input
                      type="text"
                      value={apiKeys.azureSpeechRegion}
                      onChange={(e) => setApiKeys(prev => ({
                        ...prev,
                        azureSpeechRegion: e.target.value
                      }))}
                      placeholder="例如: eastus"
                    />
                  </div>

                  <div className="api-setting">
                    <label>Azure OpenAI API Key</label>
                    <input
                      type="password"
                      value={apiKeys.azureOpenAIKey}
                      onChange={(e) => setApiKeys(prev => ({
                        ...prev,
                        azureOpenAIKey: e.target.value
                      }))}
                      placeholder="輸入 Azure OpenAI API Key"
                    />
                  </div>

                  <div className="api-setting">
                    <label>Azure OpenAI Endpoint</label>
                    <input
                      type="text"
                      value={apiKeys.azureOpenAIEndpoint}
                      onChange={(e) => setApiKeys(prev => ({
                        ...prev,
                        azureOpenAIEndpoint: e.target.value
                      }))}
                      placeholder="例如: https://your-resource.openai.azure.com/openai/deployments/gpt-4o-mini-audio-preview/chat/completions"
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="setting-group">
              <h3>使用說明</h3>
              <div className="instructions">
                <p>• 按下 AI 模式快捷鍵開始錄音，說出指令讓 AI 理解並生成回應</p>
                <p>• 按下直接模式快捷鍵開始錄音，語音會直接轉換為文字</p>
                <p>• 錄音期間再次按下相同快捷鍵或 ESC 鍵停止錄音</p>
                <p>• 處理完成後文字會自動輸入到當前焦點的應用程式</p>
                <p>• 首次使用請先在 API 配置中設定您的 Azure 服務金鑰</p>
              </div>
            </div>
          </div>

          <div className="settings-footer">
            <button className="cancel-btn" onClick={handleCancel}>
              取消
            </button>
            <button className="save-btn" onClick={handleSave}>
              <Save size={16} />
              儲存設定
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default SettingsPanel
