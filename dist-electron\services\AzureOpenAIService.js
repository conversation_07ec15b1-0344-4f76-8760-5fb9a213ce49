"use strict";
/**
 * Azure OpenAI Service 整合
 * 處理 AI 語音助手功能
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AzureOpenAIService = void 0;
const axios_1 = __importDefault(require("axios"));
class AzureOpenAIService {
    constructor(config) {
        this.apiKey = config.apiKey;
        this.endpoint = config.endpoint;
        this.model = config.model || 'gpt-4o-mini-audio-preview';
        this.apiVersion = '2024-10-01-preview'; // 支援音頻的 API 版本
        // 設定 axios 預設配置
        this.client = axios_1.default.create({
            headers: {
                'api-key': this.apiKey,
                'Content-Type': 'application/json'
            },
            timeout: 60000 // 60秒超時
        });
        console.log('Azure OpenAI Service initialized successfully');
    }
    /**
     * 處理音頻輸入並生成 AI 回應
     */
    async processAudioWithAI(audioBase64, userPrompt) {
        try {
            console.log('Processing audio with Azure OpenAI, data size:', audioBase64.length);
            // 構建請求 URL
            const url = `${this.endpoint}?api-version=${this.apiVersion}`;
            // 構建訊息內容
            const messages = [
                {
                    role: 'system',
                    content: `你是一個智能語音助手。用戶會通過語音與你交流，你需要：
1. 理解用戶的語音內容和意圖
2. 根據用戶的要求提供有用的回應
3. 如果用戶要求寫作（如文章、郵件、報告等），請直接提供完整的內容
4. 回應要簡潔、實用，適合直接輸入到應用程式中
5. 使用繁體中文回應`
                },
                {
                    role: 'user',
                    content: [
                        {
                            type: 'audio',
                            audio: {
                                data: audioBase64,
                                format: 'wav'
                            }
                        }
                    ]
                }
            ];
            // 如果有額外的用戶提示，添加到訊息中
            if (userPrompt) {
                messages.push({
                    role: 'user',
                    content: userPrompt
                });
            }
            // 構建請求體
            const requestBody = {
                model: this.model,
                messages: messages,
                max_tokens: 1000,
                temperature: 0.7
            };
            console.log('Sending request to Azure OpenAI...');
            const response = await this.client.post(url, requestBody);
            if (response.data.choices && response.data.choices.length > 0) {
                const aiResponse = response.data.choices[0].message.content;
                console.log('AI response received:', aiResponse.substring(0, 100) + '...');
                return aiResponse;
            }
            else {
                throw new Error('Azure OpenAI 沒有返回有效的回應');
            }
        }
        catch (error) {
            console.error('Error in processAudioWithAI:', error);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
                if (error.response.status === 401) {
                    throw new Error('API 金鑰無效或已過期');
                }
                else if (error.response.status === 429) {
                    throw new Error('API 使用額度已達上限，請稍後重試');
                }
                else if (error.response.status === 400) {
                    throw new Error('請求格式錯誤，請檢查音頻格式');
                }
            }
            throw new Error(`Azure OpenAI 處理失敗: ${error.message}`);
        }
    }
    /**
     * 生成文字回應（不使用音頻輸入）
     */
    async generateResponse(text) {
        try {
            console.log('Generating response for text:', text.substring(0, 100) + '...');
            const url = `${this.endpoint}?api-version=${this.apiVersion}`;
            const messages = [
                {
                    role: 'system',
                    content: `你是一個智能語音助手。用戶會通過文字與你交流，你需要：
1. 理解用戶的內容和意圖
2. 根據用戶的要求提供有用的回應
3. 如果用戶要求寫作（如文章、郵件、報告等），請直接提供完整的內容
4. 回應要簡潔、實用，適合直接輸入到應用程式中
5. 使用繁體中文回應`
                },
                {
                    role: 'user',
                    content: text
                }
            ];
            const requestBody = {
                model: this.model,
                messages: messages,
                max_tokens: 1000,
                temperature: 0.7
            };
            console.log('Sending text request to Azure OpenAI...');
            const response = await this.client.post(url, requestBody);
            if (response.data.choices && response.data.choices.length > 0) {
                const aiResponse = response.data.choices[0].message.content;
                console.log('AI text response received:', aiResponse.substring(0, 100) + '...');
                return aiResponse;
            }
            else {
                throw new Error('Azure OpenAI 沒有返回有效的回應');
            }
        }
        catch (error) {
            console.error('Error in generateResponse:', error);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
                if (error.response.status === 401) {
                    throw new Error('API 金鑰無效或已過期');
                }
                else if (error.response.status === 429) {
                    throw new Error('API 使用額度已達上限，請稍後重試');
                }
            }
            throw new Error(`Azure OpenAI 處理失敗: ${error.message}`);
        }
    }
    /**
     * 測試 API 連接
     */
    async testConnection() {
        try {
            await this.generateResponse('測試連接');
            return true;
        }
        catch (error) {
            console.error('Connection test failed:', error);
            return false;
        }
    }
}
exports.AzureOpenAIService = AzureOpenAIService;
//# sourceMappingURL=AzureOpenAIService.js.map