# Speech API - 可重用的語音處理 API

這是一個可重用的語音處理 API 包，整合了 Azure Speech Service 和 Azure OpenAI Service，可以輕鬆複製到其他專案使用。

## 功能特性

- 🎤 **語音轉文字**: 使用 Azure Speech Service 進行高精度語音識別
- 🤖 **AI 語音助手**: 結合語音識別和 AI 生成智能回應
- 🔧 **音頻格式處理**: 自動驗證和轉換音頻格式以符合 Azure 要求
- 📱 **跨平台支援**: 支援瀏覽器和 Node.js 環境
- 🛠️ **易於整合**: 簡單的 API 設計，易於整合到現有專案

## 音頻格式要求

根據 Azure Speech Service 官方要求：
- **格式**: WAV (PCM)
- **採樣率**: 16 kHz (推薦) 或 8 kHz
- **位深度**: 16-bit
- **聲道**: 單聲道 (mono)

## 快速開始

### 1. 安裝依賴

```bash
npm install microsoft-cognitiveservices-speech-sdk axios
```

### 2. 基本使用

```typescript
import { SpeechAPI } from './api/SpeechAPI'

// 配置 API
const config = {
  azureSpeech: {
    subscriptionKey: 'your-speech-key',
    region: 'your-region',
    language: 'zh-TW'
  },
  azureOpenAI: {
    apiKey: 'your-openai-key',
    endpoint: 'your-openai-endpoint',
    model: 'gpt-4o-mini-audio-preview'
  }
}

// 初始化 API
const speechAPI = new SpeechAPI()
await speechAPI.initialize(config)

// 語音轉文字
const audioBlob = // ... 你的音頻 Blob
const result = await speechAPI.processAudio(audioBlob, { 
  mode: 'speech-to-text' 
})
console.log('識別結果:', result.text)

// AI 語音助手
const aiResult = await speechAPI.processAudio(audioBlob, { 
  mode: 'ai-assistant' 
})
console.log('AI 回應:', aiResult.text)
```

### 3. 音頻格式驗證

```typescript
import { AudioProcessor } from './api/AudioProcessor'

// 驗證音頻格式
const validation = await AudioProcessor.validateAudioFormat(audioBlob)
if (!validation.isValid) {
  console.log('音頻格式問題:', validation.issues)
  
  // 自動轉換格式
  const convertedBlob = await AudioProcessor.convertAudioFormat(audioBlob)
}
```

## API 參考

### SpeechAPI

#### 方法

##### `initialize(config: SpeechAPIConfig): Promise<void>`
初始化 API 服務

##### `processAudio(audioData: string | Blob, options: AudioProcessingOptions): Promise<SpeechResult | AIResponse>`
處理音頻數據的主要入口點

**參數:**
- `audioData`: 音頻數據 (Base64 字串或 Blob)
- `options.mode`: 處理模式 ('speech-to-text' | 'ai-assistant')

##### `speechToText(audioData: string | Blob): Promise<SpeechResult>`
純語音轉文字

##### `aiAssistant(audioData: string | Blob, options: AudioProcessingOptions): Promise<AIResponse>`
AI 語音助手

##### `generateTextResponse(text: string): Promise<string>`
直接文字生成 AI 回應

##### `testConnection(): Promise<{speechService: boolean, openAIService: boolean}>`
測試服務連接

### AudioProcessor

#### 靜態方法

##### `validateAudioFormat(audioBlob: Blob): Promise<AudioValidationResult>`
驗證音頻格式

##### `convertAudioFormat(audioBlob: Blob): Promise<Blob>`
轉換音頻格式以符合 Azure 要求

##### `getAudioInfo(audioBlob: Blob): Promise<AudioFormat>`
獲取音頻檔案資訊

## 錯誤處理

API 提供完整的錯誤處理機制：

```typescript
// 設定錯誤處理回調
speechAPI.onError((error) => {
  console.error('API 錯誤:', error)
})

// 設定通知回調
speechAPI.onNotification((message, type) => {
  console.log(`${type}: ${message}`)
})
```

## 環境配置

### Azure Speech Service
1. 在 Azure Portal 創建 Speech Service 資源
2. 獲取訂閱金鑰和區域
3. 設定環境變數或配置檔案

### Azure OpenAI Service
1. 在 Azure Portal 創建 OpenAI 資源
2. 部署 GPT 模型
3. 獲取 API 金鑰和端點

## 複製到其他專案

要將此 API 複製到其他專案：

1. **複製檔案**:
   ```
   src/api/
   ├── SpeechAPI.ts
   ├── AudioProcessor.ts
   └── README.md
   
   src/services/
   ├── AzureSpeechService.ts
   ├── AzureOpenAIService.ts
   ├── ErrorHandler.ts
   └── AudioRecorder.ts (如果需要錄音功能)
   ```

2. **安裝依賴**:
   ```bash
   npm install microsoft-cognitiveservices-speech-sdk axios
   ```

3. **配置服務**:
   ```typescript
   import { SpeechAPI } from './path/to/SpeechAPI'
   
   const speechAPI = new SpeechAPI()
   await speechAPI.initialize(yourConfig)
   ```

## 注意事項

- 確保音頻格式符合 Azure Speech Service 要求
- 在生產環境中妥善保護 API 金鑰
- 考慮 API 使用額度和成本
- 測試不同音頻品質和環境下的表現

## 授權

此 API 包可自由使用和修改，適合整合到商業和開源專案中。
