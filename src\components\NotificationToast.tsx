import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, AlertCircle, Info, X, AlertTriangle } from 'lucide-react'
import './NotificationToast.css'

interface Notification {
  id: string
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

interface NotificationToastProps {
  notifications: Notification[]
  onRemove: (id: string) => void
}

const NotificationToast: React.FC<NotificationToastProps> = ({ notifications, onRemove }) => {
  const getIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle size={20} />
      case 'error':
        return <AlertCircle size={20} />
      case 'warning':
        return <AlertTriangle size={20} />
      case 'info':
      default:
        return <Info size={20} />
    }
  }

  const getTypeClass = (type: string) => {
    switch (type) {
      case 'success':
        return 'toast-success'
      case 'error':
        return 'toast-error'
      case 'warning':
        return 'toast-warning'
      case 'info':
      default:
        return 'toast-info'
    }
  }

  return (
    <div className="notification-container">
      <AnimatePresence>
        {notifications.map((notification) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onRemove={onRemove}
            getIcon={getIcon}
            getTypeClass={getTypeClass}
          />
        ))}
      </AnimatePresence>
    </div>
  )
}

interface NotificationItemProps {
  notification: Notification
  onRemove: (id: string) => void
  getIcon: (type: string) => React.ReactNode
  getTypeClass: (type: string) => string
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onRemove,
  getIcon,
  getTypeClass
}) => {
  useEffect(() => {
    const duration = notification.duration || 3000
    const timer = setTimeout(() => {
      onRemove(notification.id)
    }, duration)

    return () => clearTimeout(timer)
  }, [notification.id, notification.duration, onRemove])

  return (
    <motion.div
      className={`notification-toast ${getTypeClass(notification.type)}`}
      initial={{ opacity: 0, x: 300, scale: 0.8 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.8 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      layout
    >
      <div className="toast-content">
        <div className="toast-icon">
          {getIcon(notification.type)}
        </div>
        <div className="toast-message">
          {notification.message}
        </div>
        <button
          className="toast-close"
          onClick={() => onRemove(notification.id)}
        >
          <X size={16} />
        </button>
      </div>
      
      <motion.div
        className="toast-progress"
        initial={{ width: "100%" }}
        animate={{ width: "0%" }}
        transition={{ 
          duration: (notification.duration || 3000) / 1000,
          ease: "linear"
        }}
      />
    </motion.div>
  )
}

export default NotificationToast
