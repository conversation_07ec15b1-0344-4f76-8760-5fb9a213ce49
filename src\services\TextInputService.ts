/**
 * 文字輸入服務
 * 處理自動文字輸入到當前焦點的應用程式
 */

import { clipboard } from 'electron'
import { keyboard, Key } from '@nut-tree-fork/nut-js'

export class TextInputService {
  constructor() {
    // 設定 nut.js 的配置
    keyboard.config.autoDelayMs = 10
    console.log('Text Input Service initialized with nut.js')
  }

  /**
   * 直接輸入文字（使用 nut.js）
   */
  async typeText(text: string): Promise<boolean> {
    try {
      console.log('Typing text directly with nut.js:', text.substring(0, 50) + '...')

      // 等待一小段時間確保焦點正確
      await this.delay(100)

      // 使用 nut.js 直接輸入文字
      await keyboard.type(text)

      console.log('Text typed successfully')
      return true
    } catch (error) {
      console.error('Direct typing failed:', error)
      console.log('Falling back to clipboard method')
      return await this.typeTextViaClipboard(text)
    }
  }

  /**
   * 使用剪貼簿輸入文字（備用方案）
   */
  async typeTextViaClipboard(text: string): Promise<boolean> {
    try {
      console.log('Typing text via clipboard:', text.substring(0, 50) + '...')
      
      // 保存當前剪貼簿內容
      const originalClipboard = clipboard.readText()
      
      // 將文字複製到剪貼簿
      clipboard.writeText(text)
      
      // 等待一小段時間確保剪貼簿更新
      await this.delay(50)
      
      // 模擬 Ctrl+V 貼上
      await keyboard.pressKey(Key.LeftControl, Key.V)
      await keyboard.releaseKey(Key.LeftControl, Key.V)
      
      // 等待貼上完成
      await this.delay(100)
      
      // 恢復原始剪貼簿內容
      setTimeout(() => {
        clipboard.writeText(originalClipboard)
      }, 1000)
      
      console.log('Text pasted successfully via clipboard')
      return true
    } catch (error) {
      console.error('Clipboard typing failed:', error)
      return false
    }
  }

  /**
   * 智能文字輸入（自動選擇最佳方法）
   */
  async smartTypeText(text: string): Promise<boolean> {
    try {
      // 檢查文字長度，決定使用哪種方法
      if (text.length > 1000) {
        console.log('Text is long, using clipboard method')
        return await this.typeTextViaClipboard(text)
      }
      
      // 檢查是否包含特殊字符
      if (this.hasSpecialCharacters(text)) {
        console.log('Text contains special characters, using clipboard method')
        return await this.typeTextViaClipboard(text)
      }
      
      // 嘗試直接輸入
      console.log('Using direct typing method')
      return await this.typeText(text)
      
    } catch (error) {
      console.error('Smart typing failed:', error)
      return false
    }
  }

  /**
   * 檢查文字是否包含特殊字符
   */
  private hasSpecialCharacters(text: string): boolean {
    // 檢查是否包含複雜的 Unicode 字符或特殊符號
    const specialCharRegex = /[^\u0000-\u007F\u4e00-\u9fff\u3400-\u4dbf]/
    return specialCharRegex.test(text)
  }

  /**
   * 模擬按鍵組合
   */
  async pressKeyCombo(keys: any[]): Promise<boolean> {
    try {
      await keyboard.pressKey(...keys)
      await keyboard.releaseKey(...keys)
      return true
    } catch (error) {
      console.error('Key combo failed:', error)
      return false
    }
  }

  /**
   * 模擬單個按鍵
   */
  async pressKey(key: any): Promise<boolean> {
    try {
      await keyboard.pressKey(key)
      await keyboard.releaseKey(key)
      return true
    } catch (error) {
      console.error('Key press failed:', error)
      return false
    }
  }

  /**
   * 清除當前輸入（模擬 Ctrl+A 然後刪除）
   */
  async clearCurrentInput(): Promise<boolean> {
    try {
      // 全選
      await keyboard.pressKey(Key.LeftControl, Key.A)
      await keyboard.releaseKey(Key.LeftControl, Key.A)
      
      await this.delay(50)
      
      // 刪除
      await keyboard.pressKey(Key.Delete)
      await keyboard.releaseKey(Key.Delete)
      
      return true
    } catch (error) {
      console.error('Clear input failed:', error)
      return false
    }
  }

  /**
   * 模擬 Enter 鍵
   */
  async pressEnter(): Promise<boolean> {
    return await this.pressKey(Key.Return)
  }

  /**
   * 模擬 Tab 鍵
   */
  async pressTab(): Promise<boolean> {
    return await this.pressKey(Key.Tab)
  }

  /**
   * 模擬 Escape 鍵
   */
  async pressEscape(): Promise<boolean> {
    return await this.pressKey(Key.Escape)
  }

  /**
   * 延遲函數
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 測試文字輸入功能
   */
  async testTextInput(): Promise<boolean> {
    try {
      console.log('Testing text input functionality...')
      
      // 測試簡單文字輸入
      const testText = '測試文字輸入功能'
      const success = await this.smartTypeText(testText)
      
      if (success) {
        console.log('Text input test passed')
        return true
      } else {
        console.log('Text input test failed')
        return false
      }
    } catch (error) {
      console.error('Text input test error:', error)
      return false
    }
  }

  /**
   * 獲取服務狀態
   */
  getStatus(): { available: boolean; method: string } {
    try {
      // 檢查 nut.js 是否可用
      return {
        available: true,
        method: 'nut.js with clipboard fallback'
      }
    } catch (error) {
      return {
        available: false,
        method: 'unavailable'
      }
    }
  }
}
