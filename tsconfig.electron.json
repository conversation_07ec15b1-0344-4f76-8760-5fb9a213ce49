{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist-electron", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true}, "include": ["src/main/**/*", "src/services/**/*"], "exclude": ["node_modules", "dist", "dist-electron", "src/components/**/*", "src/App.tsx", "src/main.tsx", "src/*.css", "src/services/AudioRecorder.ts"]}