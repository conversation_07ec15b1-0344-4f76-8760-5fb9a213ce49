/**
 * Azure OpenAI Service 整合
 * 處理 AI 語音助手功能
 */

const axios = require('axios')

class AzureOpenAIService {
  constructor(config) {
    this.apiKey = config.apiKey
    this.endpoint = config.endpoint
    this.model = config.model || 'gpt-4o-mini-audio-preview'
    this.apiVersion = '2024-10-01-preview' // 支援音頻的 API 版本
    
    // 設定 axios 預設配置
    this.client = axios.create({
      headers: {
        'api-key': this.apiKey,
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超時
    })
    
    console.log('Azure OpenAI Service initialized successfully')
  }

  /**
   * 處理音頻輸入並生成 AI 回應
   */
  async processAudioWithAI(audioBase64, userPrompt = null) {
    try {
      console.log('Processing audio with Azure OpenAI, data size:', audioBase64.length)
      
      // 構建請求 URL
      const url = `${this.endpoint}?api-version=${this.apiVersion}`
      
      // 構建訊息內容
      const messages = [
        {
          role: 'system',
          content: `你是一個智能語音助手。用戶會通過語音與你交流，你需要：
1. 理解用戶的語音內容和意圖
2. 根據用戶的要求提供有用的回應
3. 如果用戶要求寫作（如文章、郵件、報告等），請直接提供完整的內容
4. 回應要簡潔、實用，適合直接輸入到應用程式中
5. 使用繁體中文回應`
        },
        {
          role: 'user',
          content: [
            {
              type: 'input_audio',
              input_audio: {
                data: audioBase64,
                format: 'webm'
              }
            }
          ]
        }
      ]
      
      // 如果有額外的文字提示，添加到訊息中
      if (userPrompt) {
        messages.push({
          role: 'user',
          content: userPrompt
        })
      }
      
      const requestData = {
        model: this.model,
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
        modalities: ['text'],
        audio: {
          voice: 'alloy',
          format: 'wav'
        }
      }
      
      console.log('Sending request to Azure OpenAI...')
      
      const response = await this.client.post(url, requestData)
      
      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const choice = response.data.choices[0]
        const content = choice.message.content
        
        console.log('Azure OpenAI response received:', content)
        
        return {
          text: content,
          usage: response.data.usage,
          model: response.data.model
        }
      } else {
        throw new Error('Azure OpenAI 回應格式異常')
      }
      
    } catch (error) {
      console.error('Azure OpenAI processing error:', error)
      
      if (error.response) {
        const status = error.response.status
        const errorData = error.response.data
        
        if (status === 401) {
          throw new Error('API 金鑰無效，請檢查設定')
        } else if (status === 429) {
          throw new Error('API 請求頻率過高，請稍後重試')
        } else if (status === 400) {
          throw new Error(`請求格式錯誤: ${errorData.error?.message || '未知錯誤'}`)
        } else {
          throw new Error(`Azure OpenAI 錯誤 (${status}): ${errorData.error?.message || error.message}`)
        }
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('請求超時，請檢查網路連接')
      } else {
        throw new Error(`處理失敗: ${error.message}`)
      }
    }
  }

  /**
   * 處理純文字輸入（用於測試或備用）
   */
  async processTextWithAI(text) {
    try {
      const url = `${this.endpoint}?api-version=${this.apiVersion}`
      
      const requestData = {
        model: this.model,
        messages: [
          {
            role: 'system',
            content: `你是一個智能助手。根據用戶的要求提供有用的回應。
如果用戶要求寫作，請直接提供完整的內容。
回應要簡潔、實用，適合直接輸入到應用程式中。
使用繁體中文回應。`
          },
          {
            role: 'user',
            content: text
          }
        ],
        max_tokens: 1000,
        temperature: 0.7
      }
      
      const response = await this.client.post(url, requestData)
      
      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content
        
        return {
          text: content,
          usage: response.data.usage,
          model: response.data.model
        }
      } else {
        throw new Error('Azure OpenAI 回應格式異常')
      }
      
    } catch (error) {
      console.error('Azure OpenAI text processing error:', error)
      throw error
    }
  }

  /**
   * 測試 API 連接
   */
  async testConnection() {
    try {
      const result = await this.processTextWithAI('測試連接')
      return true
    } catch (error) {
      console.error('Azure OpenAI connection test failed:', error)
      return false
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config) {
    this.apiKey = config.apiKey
    this.endpoint = config.endpoint
    this.model = config.model || 'gpt-4o-mini-audio-preview'
    
    // 更新 axios 客戶端
    this.client.defaults.headers['api-key'] = this.apiKey
    
    console.log('Azure OpenAI Service configuration updated')
  }

  /**
   * 獲取當前配置
   */
  getConfig() {
    return {
      endpoint: this.endpoint,
      model: this.model,
      apiVersion: this.apiVersion
    }
  }

  /**
   * 釋放資源
   */
  dispose() {
    this.client = null
    console.log('Azure OpenAI Service disposed')
  }
}

module.exports = AzureOpenAIService
