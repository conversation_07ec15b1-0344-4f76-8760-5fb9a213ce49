"use strict";
/**
 * 文字輸入服務
 * 處理自動文字輸入到當前焦點的應用程式
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextInputService = void 0;
const electron_1 = require("electron");
const nut_js_1 = require("@nut-tree-fork/nut-js");
class TextInputService {
    constructor() {
        // 設定 nut.js 的配置
        nut_js_1.keyboard.config.autoDelayMs = 10;
        console.log('Text Input Service initialized with nut.js');
    }
    /**
     * 直接輸入文字（使用 nut.js）
     */
    async typeText(text) {
        try {
            console.log('Typing text directly with nut.js:', text.substring(0, 50) + '...');
            // 等待一小段時間確保焦點正確
            await this.delay(100);
            // 使用 nut.js 直接輸入文字
            await nut_js_1.keyboard.type(text);
            console.log('Text typed successfully');
            return true;
        }
        catch (error) {
            console.error('Direct typing failed:', error);
            console.log('Falling back to clipboard method');
            return await this.typeTextViaClipboard(text);
        }
    }
    /**
     * 使用剪貼簿輸入文字（備用方案）
     */
    async typeTextViaClipboard(text) {
        try {
            console.log('Typing text via clipboard:', text.substring(0, 50) + '...');
            // 保存當前剪貼簿內容
            const originalClipboard = electron_1.clipboard.readText();
            // 將文字複製到剪貼簿
            electron_1.clipboard.writeText(text);
            // 等待一小段時間確保剪貼簿更新
            await this.delay(50);
            // 模擬 Ctrl+V 貼上
            await nut_js_1.keyboard.pressKey(nut_js_1.Key.LeftControl, nut_js_1.Key.V);
            await nut_js_1.keyboard.releaseKey(nut_js_1.Key.LeftControl, nut_js_1.Key.V);
            // 等待貼上完成
            await this.delay(100);
            // 恢復原始剪貼簿內容
            setTimeout(() => {
                electron_1.clipboard.writeText(originalClipboard);
            }, 1000);
            console.log('Text pasted successfully via clipboard');
            return true;
        }
        catch (error) {
            console.error('Clipboard typing failed:', error);
            return false;
        }
    }
    /**
     * 智能文字輸入（自動選擇最佳方法）
     */
    async smartTypeText(text) {
        try {
            // 檢查文字長度，決定使用哪種方法
            if (text.length > 1000) {
                console.log('Text is long, using clipboard method');
                return await this.typeTextViaClipboard(text);
            }
            // 檢查是否包含特殊字符
            if (this.hasSpecialCharacters(text)) {
                console.log('Text contains special characters, using clipboard method');
                return await this.typeTextViaClipboard(text);
            }
            // 嘗試直接輸入
            console.log('Using direct typing method');
            return await this.typeText(text);
        }
        catch (error) {
            console.error('Smart typing failed:', error);
            return false;
        }
    }
    /**
     * 檢查文字是否包含特殊字符
     */
    hasSpecialCharacters(text) {
        // 檢查是否包含複雜的 Unicode 字符或特殊符號
        const specialCharRegex = /[^\u0000-\u007F\u4e00-\u9fff\u3400-\u4dbf]/;
        return specialCharRegex.test(text);
    }
    /**
     * 模擬按鍵組合
     */
    async pressKeyCombo(keys) {
        try {
            await nut_js_1.keyboard.pressKey(...keys);
            await nut_js_1.keyboard.releaseKey(...keys);
            return true;
        }
        catch (error) {
            console.error('Key combo failed:', error);
            return false;
        }
    }
    /**
     * 模擬單個按鍵
     */
    async pressKey(key) {
        try {
            await nut_js_1.keyboard.pressKey(key);
            await nut_js_1.keyboard.releaseKey(key);
            return true;
        }
        catch (error) {
            console.error('Key press failed:', error);
            return false;
        }
    }
    /**
     * 清除當前輸入（模擬 Ctrl+A 然後刪除）
     */
    async clearCurrentInput() {
        try {
            // 全選
            await nut_js_1.keyboard.pressKey(nut_js_1.Key.LeftControl, nut_js_1.Key.A);
            await nut_js_1.keyboard.releaseKey(nut_js_1.Key.LeftControl, nut_js_1.Key.A);
            await this.delay(50);
            // 刪除
            await nut_js_1.keyboard.pressKey(nut_js_1.Key.Delete);
            await nut_js_1.keyboard.releaseKey(nut_js_1.Key.Delete);
            return true;
        }
        catch (error) {
            console.error('Clear input failed:', error);
            return false;
        }
    }
    /**
     * 模擬 Enter 鍵
     */
    async pressEnter() {
        return await this.pressKey(nut_js_1.Key.Return);
    }
    /**
     * 模擬 Tab 鍵
     */
    async pressTab() {
        return await this.pressKey(nut_js_1.Key.Tab);
    }
    /**
     * 模擬 Escape 鍵
     */
    async pressEscape() {
        return await this.pressKey(nut_js_1.Key.Escape);
    }
    /**
     * 延遲函數
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * 測試文字輸入功能
     */
    async testTextInput() {
        try {
            console.log('Testing text input functionality...');
            // 測試簡單文字輸入
            const testText = '測試文字輸入功能';
            const success = await this.smartTypeText(testText);
            if (success) {
                console.log('Text input test passed');
                return true;
            }
            else {
                console.log('Text input test failed');
                return false;
            }
        }
        catch (error) {
            console.error('Text input test error:', error);
            return false;
        }
    }
    /**
     * 獲取服務狀態
     */
    getStatus() {
        try {
            // 檢查 nut.js 是否可用
            return {
                available: true,
                method: 'nut.js with clipboard fallback'
            };
        }
        catch (error) {
            return {
                available: false,
                method: 'unavailable'
            };
        }
    }
}
exports.TextInputService = TextInputService;
//# sourceMappingURL=TextInputService.js.map