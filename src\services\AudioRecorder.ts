/**
 * AudioRecorder 服務
 * 處理音頻錄製功能，使用 Web Audio API
 */

import { audioDeviceManager } from '../utils/AudioDeviceManager'

export interface AudioRecorderOptions {
  sampleRate?: number
  channels?: number
  bitDepth?: number
}

export interface RecordingData {
  audioBlob: Blob
  duration: number
  sampleRate: number
}

export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null
  private audioStream: MediaStream | null = null
  private audioChunks: Blob[] = []
  private startTime: number = 0
  private options: AudioRecorderOptions

  constructor(options: AudioRecorderOptions = {}) {
    this.options = {
      sampleRate: 16000, // Azure Speech Service 建議使用 16kHz
      channels: 1, // 單聲道
      bitDepth: 16,
      ...options
    }
  }

  /**
   * 請求麥克風權限並初始化錄音
   */
  async initialize(): Promise<void> {
    try {
      // 使用 AudioDeviceManager 獲取音頻流
      this.audioStream = await audioDeviceManager.getAudioStream({
        audio: {
          sampleRate: this.options.sampleRate,
          channelCount: this.options.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      console.log('Microphone access granted')
    } catch (error) {
      console.error('Failed to access microphone:', error)
      throw new Error('無法存取麥克風，請檢查權限設定')
    }
  }

  /**
   * 開始錄音
   */
  async startRecording(): Promise<void> {
    if (!this.audioStream) {
      await this.initialize()
    }

    if (!this.audioStream) {
      throw new Error('音頻流未初始化')
    }

    // 清空之前的錄音數據
    this.audioChunks = []

    // 創建 MediaRecorder
    const mimeType = this.getSupportedMimeType()
    this.mediaRecorder = new MediaRecorder(this.audioStream, {
      mimeType
    })

    // 設定事件監聽器
    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data)
      }
    }

    this.mediaRecorder.onerror = (event) => {
      console.error('MediaRecorder error:', event)
    }

    // 開始錄音
    this.startTime = Date.now()
    this.mediaRecorder.start(100) // 每100ms收集一次數據

    console.log('Recording started')
  }

  /**
   * 停止錄音
   */
  async stopRecording(): Promise<RecordingData> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
        reject(new Error('錄音器未啟動'))
        return
      }

      this.mediaRecorder.onstop = async () => {
        try {
          const duration = Date.now() - this.startTime
          const audioBlob = new Blob(this.audioChunks, {
            type: this.getSupportedMimeType()
          })

          // 轉換為 WAV 格式
          const wavBlob = await this.convertToWav(audioBlob)

          const recordingData: RecordingData = {
            audioBlob: wavBlob,
            duration,
            sampleRate: this.options.sampleRate || 16000
          }

          console.log('Recording stopped, duration:', duration, 'ms')
          resolve(recordingData)
        } catch (error) {
          reject(error)
        }
      }

      this.mediaRecorder.stop()
    })
  }

  /**
   * 取消錄音
   */
  cancelRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop()
    }
    this.audioChunks = []
    console.log('Recording cancelled')
  }

  /**
   * 釋放資源
   */
  dispose(): void {
    this.cancelRecording()
    
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop())
      this.audioStream = null
    }

    this.mediaRecorder = null
    console.log('AudioRecorder disposed')
  }

  /**
   * 檢查是否正在錄音
   */
  isRecording(): boolean {
    return this.mediaRecorder?.state === 'recording'
  }

  /**
   * 獲取支援的 MIME 類型
   */
  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav'
    ]

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type
      }
    }

    return 'audio/webm' // 預設值
  }

  /**
   * 將 Blob 轉換為 ArrayBuffer
   */
  static async blobToArrayBuffer(blob: Blob): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as ArrayBuffer)
      reader.onerror = reject
      reader.readAsArrayBuffer(blob)
    })
  }

  /**
   * 將 Blob 轉換為 Base64
   */
  static async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // 移除 data:audio/webm;base64, 前綴
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 將 WebM 音頻轉換為 WAV 格式
   */
  private async convertToWav(webmBlob: Blob): Promise<Blob> {
    try {
      // 創建 AudioContext
      const audioContext = new AudioContext()

      // 將 Blob 轉換為 ArrayBuffer
      const arrayBuffer = await AudioRecorder.blobToArrayBuffer(webmBlob)

      // 解碼音頻數據
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

      // 轉換為 WAV 格式
      const wavArrayBuffer = this.audioBufferToWav(audioBuffer)

      // 創建 WAV Blob
      const wavBlob = new Blob([wavArrayBuffer], { type: 'audio/wav' })

      // 清理 AudioContext
      audioContext.close()

      return wavBlob
    } catch (error) {
      console.error('Error converting to WAV:', error)
      // 如果轉換失敗，返回原始 Blob
      return webmBlob
    }
  }

  /**
   * 將 AudioBuffer 轉換為 WAV ArrayBuffer
   */
  private audioBufferToWav(buffer: AudioBuffer): ArrayBuffer {
    const length = buffer.length
    const numberOfChannels = buffer.numberOfChannels
    const sampleRate = buffer.sampleRate
    const bytesPerSample = 2 // 16-bit
    const blockAlign = numberOfChannels * bytesPerSample
    const byteRate = sampleRate * blockAlign
    const dataSize = length * blockAlign
    const bufferSize = 44 + dataSize

    const arrayBuffer = new ArrayBuffer(bufferSize)
    const view = new DataView(arrayBuffer)

    // WAV 標頭
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i))
      }
    }

    // RIFF 標識符
    writeString(0, 'RIFF')
    view.setUint32(4, bufferSize - 8, true)
    writeString(8, 'WAVE')

    // fmt 子塊
    writeString(12, 'fmt ')
    view.setUint32(16, 16, true) // fmt 塊大小
    view.setUint16(20, 1, true) // 音頻格式 (PCM)
    view.setUint16(22, numberOfChannels, true)
    view.setUint32(24, sampleRate, true)
    view.setUint32(28, byteRate, true)
    view.setUint16(32, blockAlign, true)
    view.setUint16(34, 16, true) // 位深度

    // data 子塊
    writeString(36, 'data')
    view.setUint32(40, dataSize, true)

    // 寫入音頻數據
    let offset = 44
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]))
        view.setInt16(offset, sample * 0x7FFF, true)
        offset += 2
      }
    }

    return arrayBuffer
  }
}

export default AudioRecorder
