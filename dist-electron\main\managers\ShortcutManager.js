"use strict";
/**
 * 快捷鍵管理器
 * 負責註冊和管理全域快捷鍵
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShortcutManager = void 0;
const electron_1 = require("electron");
const RecordingController_1 = require("../controllers/RecordingController");
class ShortcutManager {
    constructor(windowManager) {
        this.hotkeys = {
            aiMode: 'CommandOrControl+Shift+C',
            directMode: 'CommandOrControl+Shift+V'
        };
        this.windowManager = windowManager;
        this.recordingController = new RecordingController_1.RecordingController(windowManager);
    }
    /**
     * 註冊全域快捷鍵
     */
    registerShortcuts() {
        this.unregisterAll(); // 先清除現有的快捷鍵
        try {
            // AI 模式快捷鍵
            const aiModeRegistered = electron_1.globalShortcut.register(this.hotkeys.aiMode, () => {
                console.log('AI mode hotkey triggered');
                this.recordingController.handleVoiceInput('ai');
            });
            // 直接模式快捷鍵
            const directModeRegistered = electron_1.globalShortcut.register(this.hotkeys.directMode, () => {
                console.log('Direct mode hotkey triggered');
                this.recordingController.handleVoiceInput('direct');
            });
            if (aiModeRegistered && directModeRegistered) {
                console.log('Global shortcuts registered:', this.hotkeys);
            }
            else {
                console.warn('Some shortcuts failed to register');
            }
        }
        catch (error) {
            console.error('Failed to register shortcuts:', error);
        }
    }
    /**
     * 更新快捷鍵配置
     */
    updateHotkeys(newHotkeys) {
        this.hotkeys = { ...newHotkeys };
        this.registerShortcuts();
    }
    /**
     * 取消註冊所有快捷鍵
     */
    unregisterAll() {
        electron_1.globalShortcut.unregisterAll();
    }
    /**
     * 獲取當前快捷鍵配置
     */
    getHotkeys() {
        return { ...this.hotkeys };
    }
    /**
     * 獲取錄音控制器
     */
    getRecordingController() {
        return this.recordingController;
    }
}
exports.ShortcutManager = ShortcutManager;
//# sourceMappingURL=ShortcutManager.js.map