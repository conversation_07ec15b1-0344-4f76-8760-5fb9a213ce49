/**
 * SpeechPilot 應用程式測試腳本
 * 用於驗證所有核心功能是否正常工作
 */

const { spawn } = require('child_process')
const path = require('path')

console.log('🚀 開始測試 SpeechPilot 應用程式...\n')

// 測試項目清單
const tests = [
  {
    name: '檢查依賴安裝',
    description: '驗證所有必要的 npm 包是否已安裝',
    test: checkDependencies
  },
  {
    name: '檢查配置檔案',
    description: '驗證環境變數和配置檔案是否存在',
    test: checkConfiguration
  },
  {
    name: '檢查服務初始化',
    description: '驗證所有服務是否能正確初始化',
    test: checkServices
  },
  {
    name: '檢查 UI 組件',
    description: '驗證 React 組件是否能正確編譯',
    test: checkUIComponents
  }
]

async function runTests() {
  let passedTests = 0
  let totalTests = tests.length

  for (const test of tests) {
    console.log(`📋 測試: ${test.name}`)
    console.log(`   描述: ${test.description}`)
    
    try {
      const result = await test.test()
      if (result.success) {
        console.log(`   ✅ 通過: ${result.message}`)
        passedTests++
      } else {
        console.log(`   ❌ 失敗: ${result.message}`)
      }
    } catch (error) {
      console.log(`   ❌ 錯誤: ${error.message}`)
    }
    
    console.log('')
  }

  console.log('📊 測試結果總結:')
  console.log(`   通過: ${passedTests}/${totalTests}`)
  console.log(`   成功率: ${Math.round((passedTests / totalTests) * 100)}%`)
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有測試通過！應用程式準備就緒。')
  } else {
    console.log('\n⚠️  部分測試失敗，請檢查上述錯誤訊息。')
  }
}

async function checkDependencies() {
  const fs = require('fs')
  
  try {
    // 檢查 package.json
    if (!fs.existsSync('package.json')) {
      return { success: false, message: 'package.json 檔案不存在' }
    }
    
    // 檢查 node_modules
    if (!fs.existsSync('node_modules')) {
      return { success: false, message: 'node_modules 目錄不存在，請執行 npm install' }
    }
    
    // 檢查關鍵依賴
    const criticalDeps = [
      'electron',
      'vite',
      'react',
      '@nut-tree-fork/nut-js',
      'microsoft-cognitiveservices-speech-sdk',
      'axios'
    ]
    
    for (const dep of criticalDeps) {
      if (!fs.existsSync(`node_modules/${dep}`)) {
        return { success: false, message: `關鍵依賴 ${dep} 未安裝` }
      }
    }
    
    return { success: true, message: '所有依賴已正確安裝' }
  } catch (error) {
    return { success: false, message: `檢查依賴時發生錯誤: ${error.message}` }
  }
}

async function checkConfiguration() {
  const fs = require('fs')
  
  try {
    // 檢查環境變數檔案
    const envFiles = ['.env', '.env.local']
    let envFileExists = false
    
    for (const envFile of envFiles) {
      if (fs.existsSync(envFile)) {
        envFileExists = true
        break
      }
    }
    
    if (!envFileExists) {
      return { 
        success: false, 
        message: '環境變數檔案 (.env 或 .env.local) 不存在，請創建並配置 API 金鑰' 
      }
    }
    
    // 檢查必要的配置檔案
    const configFiles = [
      'src/main.js',
      'src/App.tsx',
      'src/services/ConfigManager.js',
      'src/services/AzureSpeechService.js',
      'src/services/AzureOpenAIService.js',
      'src/services/TextInputService.js'
    ]
    
    for (const file of configFiles) {
      if (!fs.existsSync(file)) {
        return { success: false, message: `配置檔案 ${file} 不存在` }
      }
    }
    
    return { success: true, message: '所有配置檔案存在' }
  } catch (error) {
    return { success: false, message: `檢查配置時發生錯誤: ${error.message}` }
  }
}

async function checkServices() {
  try {
    // 嘗試載入服務模組
    const ConfigManager = require('./src/services/ConfigManager')
    const AzureSpeechService = require('./src/services/AzureSpeechService')
    const AzureOpenAIService = require('./src/services/AzureOpenAIService')
    const TextInputService = require('./src/services/TextInputService')
    
    // 測試配置管理器
    const configManager = new ConfigManager()
    const config = configManager.getConfig()
    
    if (!config) {
      return { success: false, message: '配置管理器初始化失敗' }
    }
    
    return { success: true, message: '所有服務模組可以正確載入' }
  } catch (error) {
    return { success: false, message: `服務初始化錯誤: ${error.message}` }
  }
}

async function checkUIComponents() {
  const fs = require('fs')
  
  try {
    // 檢查 React 組件檔案
    const components = [
      'src/App.tsx',
      'src/components/RecordingPopup.tsx',
      'src/components/SettingsPanel.tsx',
      'src/components/NotificationToast.tsx'
    ]
    
    for (const component of components) {
      if (!fs.existsSync(component)) {
        return { success: false, message: `UI 組件 ${component} 不存在` }
      }
    }
    
    // 檢查 CSS 檔案
    const cssFiles = [
      'src/App.css',
      'src/components/RecordingPopup.css',
      'src/components/SettingsPanel.css',
      'src/components/NotificationToast.css'
    ]
    
    for (const cssFile of cssFiles) {
      if (!fs.existsSync(cssFile)) {
        return { success: false, message: `CSS 檔案 ${cssFile} 不存在` }
      }
    }
    
    return { success: true, message: '所有 UI 組件和樣式檔案存在' }
  } catch (error) {
    return { success: false, message: `檢查 UI 組件時發生錯誤: ${error.message}` }
  }
}

// 執行測試
if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = { runTests }
