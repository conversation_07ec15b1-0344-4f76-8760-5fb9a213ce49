{"name": "speechpilot", "version": "1.0.0", "description": "AI語音助手桌面應用程式", "main": "dist-electron/main/index.js", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "npm run build:electron-ts && wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .", "build:electron-ts": "tsc --project tsconfig.electron.json", "build": "npm run build:vite && npm run build:electron-ts && electron-builder", "build:vite": "vite build", "build:electron": "electron-builder", "preview": "vite preview"}, "keywords": ["electron", "vite", "react", "speech", "ai"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.2.0", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^5.1.6", "wait-on": "^7.2.0"}, "dependencies": {"@nut-tree-fork/nut-js": "^4.2.6", "axios": "^1.6.7", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "microsoft-cognitiveservices-speech-sdk": "^1.45.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "build": {"appId": "com.speechpilot.app", "productName": "SpeechPilot", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}}}