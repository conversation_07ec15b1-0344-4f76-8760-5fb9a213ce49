/**
 * 音頻處理工具
 * 處理音頻格式轉換和驗證，確保符合 Azure Speech Service 要求
 */

export interface AudioFormat {
  sampleRate: number
  bitDepth: number
  channels: number
  format: string
}

export interface AudioValidationResult {
  isValid: boolean
  currentFormat: AudioFormat
  requiredFormat: AudioFormat
  issues: string[]
}

export class AudioProcessor {
  // Azure Speech Service 要求的音頻格式
  static readonly REQUIRED_FORMAT: AudioFormat = {
    sampleRate: 16000,  // 16 kHz
    bitDepth: 16,       // 16-bit
    channels: 1,        // mono
    format: 'PCM'       // PCM encoding
  }

  /**
   * 驗證音頻格式是否符合 Azure Speech Service 要求
   */
  static validateAudioFormat(audioBlob: Blob): Promise<AudioValidationResult> {
    return new Promise((resolve) => {
      const reader = new FileReader()
      
      reader.onload = () => {
        try {
          const arrayBuffer = reader.result as ArrayBuffer
          const dataView = new DataView(arrayBuffer)
          
          // 檢查是否為 WAV 檔案
          const riffHeader = String.fromCharCode(
            dataView.getUint8(0),
            dataView.getUint8(1),
            dataView.getUint8(2),
            dataView.getUint8(3)
          )
          
          if (riffHeader !== 'RIFF') {
            resolve({
              isValid: false,
              currentFormat: { sampleRate: 0, bitDepth: 0, channels: 0, format: 'Unknown' },
              requiredFormat: this.REQUIRED_FORMAT,
              issues: ['音頻格式不是 WAV 格式']
            })
            return
          }

          // 讀取 WAV 檔案資訊
          const channels = dataView.getUint16(22, true)
          const sampleRate = dataView.getUint32(24, true)
          const bitDepth = dataView.getUint16(34, true)
          
          const currentFormat: AudioFormat = {
            sampleRate,
            bitDepth,
            channels,
            format: 'WAV/PCM'
          }

          const issues: string[] = []
          
          // 檢查採樣率
          if (sampleRate !== 16000 && sampleRate !== 8000) {
            issues.push(`採樣率 ${sampleRate} Hz 不符合要求（需要 16000 Hz 或 8000 Hz）`)
          }
          
          // 檢查位深度
          if (bitDepth !== 16) {
            issues.push(`位深度 ${bitDepth} bit 不符合要求（需要 16 bit）`)
          }
          
          // 檢查聲道數
          if (channels !== 1) {
            issues.push(`聲道數 ${channels} 不符合要求（需要單聲道）`)
          }

          resolve({
            isValid: issues.length === 0,
            currentFormat,
            requiredFormat: this.REQUIRED_FORMAT,
            issues
          })
        } catch (error) {
          resolve({
            isValid: false,
            currentFormat: { sampleRate: 0, bitDepth: 0, channels: 0, format: 'Error' },
            requiredFormat: this.REQUIRED_FORMAT,
            issues: [`音頻格式解析失敗: ${error.message}`]
          })
        }
      }
      
      reader.onerror = () => {
        resolve({
          isValid: false,
          currentFormat: { sampleRate: 0, bitDepth: 0, channels: 0, format: 'Error' },
          requiredFormat: this.REQUIRED_FORMAT,
          issues: ['無法讀取音頻檔案']
        })
      }
      
      reader.readAsArrayBuffer(audioBlob)
    })
  }

  /**
   * 轉換音頻格式以符合 Azure Speech Service 要求
   * 注意：這個方法需要在瀏覽器環境中使用 Web Audio API
   */
  static async convertAudioFormat(audioBlob: Blob): Promise<Blob> {
    if (typeof window === 'undefined') {
      // Node.js 環境，返回原始音頻
      console.warn('Audio conversion not available in Node.js environment')
      return audioBlob
    }

    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      // 讀取音頻數據
      const arrayBuffer = await audioBlob.arrayBuffer()
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
      
      // 檢查是否需要轉換
      const needsConversion = 
        audioBuffer.sampleRate !== 16000 ||
        audioBuffer.numberOfChannels !== 1
      
      if (!needsConversion) {
        console.log('Audio format already correct')
        return audioBlob
      }

      console.log(`Converting audio: ${audioBuffer.sampleRate}Hz, ${audioBuffer.numberOfChannels} channels -> 16000Hz, 1 channel`)
      
      // 創建離線音頻上下文進行轉換
      const offlineContext = new OfflineAudioContext(1, audioBuffer.duration * 16000, 16000)
      
      // 創建音頻源
      const source = offlineContext.createBufferSource()
      source.buffer = audioBuffer
      
      // 如果是立體聲，需要轉換為單聲道
      if (audioBuffer.numberOfChannels > 1) {
        const merger = offlineContext.createChannelMerger(1)
        const splitter = offlineContext.createChannelSplitter(audioBuffer.numberOfChannels)
        
        source.connect(splitter)
        splitter.connect(merger, 0, 0)
        
        if (audioBuffer.numberOfChannels > 1) {
          splitter.connect(merger, 1, 0)
        }
        
        merger.connect(offlineContext.destination)
      } else {
        source.connect(offlineContext.destination)
      }
      
      source.start()
      
      // 渲染音頻
      const renderedBuffer = await offlineContext.startRendering()
      
      // 轉換為 WAV 格式
      const wavBlob = this.audioBufferToWav(renderedBuffer)
      
      console.log('Audio conversion completed')
      return wavBlob
    } catch (error) {
      console.error('Audio conversion failed:', error)
      throw new Error(`音頻轉換失敗: ${error.message}`)
    }
  }

  /**
   * 將 AudioBuffer 轉換為 WAV Blob
   */
  private static audioBufferToWav(buffer: AudioBuffer): Blob {
    const length = buffer.length
    const sampleRate = buffer.sampleRate
    const numberOfChannels = buffer.numberOfChannels
    
    // 計算 WAV 檔案大小
    const arrayBuffer = new ArrayBuffer(44 + length * 2)
    const view = new DataView(arrayBuffer)
    
    // WAV 檔案標頭
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i))
      }
    }
    
    // RIFF 標識符
    writeString(0, 'RIFF')
    // 檔案大小
    view.setUint32(4, 36 + length * 2, true)
    // WAVE 標識符
    writeString(8, 'WAVE')
    // fmt 子塊
    writeString(12, 'fmt ')
    // fmt 子塊大小
    view.setUint32(16, 16, true)
    // 音頻格式 (PCM)
    view.setUint16(20, 1, true)
    // 聲道數
    view.setUint16(22, numberOfChannels, true)
    // 採樣率
    view.setUint32(24, sampleRate, true)
    // 位元組率
    view.setUint32(28, sampleRate * numberOfChannels * 2, true)
    // 區塊對齊
    view.setUint16(32, numberOfChannels * 2, true)
    // 位深度
    view.setUint16(34, 16, true)
    // data 子塊
    writeString(36, 'data')
    // data 子塊大小
    view.setUint32(40, length * 2, true)
    
    // 寫入音頻數據
    const channelData = buffer.getChannelData(0)
    let offset = 44
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]))
      view.setInt16(offset, sample * 0x7FFF, true)
      offset += 2
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' })
  }

  /**
   * 獲取音頻檔案資訊
   */
  static async getAudioInfo(audioBlob: Blob): Promise<AudioFormat> {
    const validation = await this.validateAudioFormat(audioBlob)
    return validation.currentFormat
  }

  /**
   * 檢查瀏覽器是否支援音頻處理
   */
  static isAudioProcessingSupported(): boolean {
    return typeof window !== 'undefined' && 
           (window.AudioContext || (window as any).webkitAudioContext) !== undefined
  }
}
