{"version": 3, "file": "AzureSpeechService.js", "sourceRoot": "", "sources": ["../../src/services/AzureSpeechService.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,4EAA6D;AAc7D,MAAa,kBAAkB;IAI7B,YAAY,MAAoB;QAHxB,iBAAY,GAA4B,IAAI,CAAA;QAC5C,gBAAW,GAA2B,IAAI,CAAA;QAGhD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAoB;QAC3C,IAAI,CAAC;YACH,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,gBAAgB,CACnD,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,MAAM,CACd,CAAA;YAED,eAAe;YACf,IAAI,CAAC,YAAY,CAAC,yBAAyB,GAAG,MAAM,CAAC,QAAQ,IAAI,OAAO,CAAA;YAExE,SAAS;YACT,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAA;YAE1D,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAA;YAClE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAe;QACrC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;QACxC,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,IAAI,CAAC;gBACH,yBAAyB;gBACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;gBAE3D,SAAS;gBACT,MAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,gBAAgB,CAClD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CACzB,CAAA;gBAED,UAAU;gBACV,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;oBACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;gBAClD,CAAC;gBACD,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA;gBAE3E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBAE5B,UAAU;gBACV,UAAU,CAAC,kBAAkB,CAC3B,CAAC,MAAM,EAAE,EAAE;oBACT,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;oBAEvC,IAAI,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;wBACxD,MAAM,YAAY,GAAiB;4BACjC,IAAI,EAAE,MAAM,CAAC,IAAI;4BACjB,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;4BAC1C,QAAQ;yBACT,CAAA;wBAED,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,YAAY,CAAC,CAAA;wBAC3D,OAAO,CAAC,YAAY,CAAC,CAAA;oBACvB,CAAC;yBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;wBACtD,MAAM,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAA;oBACnC,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,IAAI,KAAK,CAAC,WAAW,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAA;oBACrD,CAAC;oBAED,UAAU,CAAC,KAAK,EAAE,CAAA;gBACpB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;oBACR,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;oBACjD,UAAU,CAAC,KAAK,EAAE,CAAA;oBAClB,MAAM,CAAC,IAAI,KAAK,CAAC,WAAW,KAAK,EAAE,CAAC,CAAC,CAAA;gBACvC,CAAC,CACF,CAAA;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;gBACnD,MAAM,CAAC,KAAK,CAAC,CAAA;YACf,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QAC3C,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;YAChD,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YACrD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,QAAgC,EAChC,OAAgC;QAEhC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;QACxC,CAAC;QAED,UAAU;QACV,MAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,0BAA0B,EAAE,CAAA;QAChE,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA;QAE3E,UAAU;QACV,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;QAC9C,CAAC,CAAA;QAED,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;gBAC1D,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YACzB,CAAC;QACH,CAAC,CAAA;QAED,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAA;YAChD,IAAI,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBAC9C,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,CAAA;YAClC,CAAC;YACD,UAAU,CAAC,8BAA8B,EAAE,CAAA;QAC7C,CAAC,CAAA;QAED,UAAU,CAAC,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACnC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;YAC1C,UAAU,CAAC,8BAA8B,EAAE,CAAA;QAC7C,CAAC,CAAA;QAED,SAAS;QACT,UAAU,CAAC,+BAA+B,EAAE,CAAA;QAE5C,OAAO,UAAU,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,UAAgC;QACxD,UAAU,CAAC,8BAA8B,CACvC,GAAG,EAAE;YACH,UAAU,CAAC,KAAK,EAAE,CAAA;YAClB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;QAC/C,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;YACnD,UAAU,CAAC,KAAK,EAAE,CAAA;QACpB,CAAC,CACF,CAAA;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAmC;QAC3D,IAAI,CAAC;YACH,gBAAgB;YAChB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,GAAG,CAAA;YAChD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;QAC5D,CAAC;QACD,OAAO,GAAG,CAAA,CAAC,QAAQ;IACrB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,IAAU;QACxC,qCAAqC;QACrC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,MAAc,EAAE,WAAmB,YAAY;QAClE,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;QACnC,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,WAAW,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAC/C,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAA;QAC7C,OAAO,IAAI,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAA;YACd,CAAC;YAED,eAAe;YACf,MAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,0BAA0B,EAAE,CAAA;YAChE,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA;YAE3E,aAAa;YACb,UAAU,CAAC,KAAK,EAAE,CAAA;YAClB,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAA;YAC9D,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAoB;QAC/B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;IAC9C,CAAC;CACF;AAnPD,gDAmPC;AAED,kBAAe,kBAAkB,CAAA"}