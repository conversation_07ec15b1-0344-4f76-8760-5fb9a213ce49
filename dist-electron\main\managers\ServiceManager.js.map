{"version": 3, "file": "ServiceManager.js", "sourceRoot": "", "sources": ["../../../src/main/managers/ServiceManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,gEAA4D;AAC5D,0EAAsE;AACtE,0EAAsE;AACtE,sEAAkE;AAClE,8DAA0D;AAE1D,MAAa,cAAc;IAOzB;QALQ,kBAAa,GAA8B,IAAI,CAAA;QAC/C,kBAAa,GAA8B,IAAI,CAAA;QAC/C,qBAAgB,GAA4B,IAAI,CAAA;QAItD,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAA;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAA;IACxC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;QAEvC,IAAI,CAAC;YACH,UAAU;YACV,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YAEpC,gBAAgB;YAChB,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YAEpC,YAAY;YACZ,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAA;YAEvC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;YACtD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAA;YAExD,IAAI,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,uCAAkB,CAAC;oBAC1C,eAAe,EAAE,MAAM,CAAC,eAAe;oBACvC,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;iBAC1B,CAAC,CAAA;gBACF,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;gBAC5D,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;YAC3C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAA;YACrF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAA;YAClE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAA;YAExD,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACrC,IAAI,CAAC,aAAa,GAAG,IAAI,uCAAkB,CAAC;oBAC1C,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,KAAK,EAAE,MAAM,CAAC,KAAK;iBACpB,CAAC,CAAA;gBACF,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;gBAC5D,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;YAC3C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAA;YACrF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAA;YAClE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B;QACtC,IAAI,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAA;YAC9C,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAA;YACzD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAA;YAChE,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAA;IAC1B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,SAAc;QAClC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;YAEzC,OAAO;YACP,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;YAE1C,YAAY;YACZ,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YACtC,CAAC;YAED,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YACtC,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAA;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;YAC3D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAA;QAExC,eAAe;QACf,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;QAE5B,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;IAC3C,CAAC;CACF;AA3KD,wCA2KC"}