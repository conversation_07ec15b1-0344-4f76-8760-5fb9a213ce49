{"version": 3, "file": "TextInputService.js", "sourceRoot": "", "sources": ["../../src/services/TextInputService.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,uCAAoC;AACpC,kDAAqD;AAErD,MAAa,gBAAgB;IAC3B;QACE,gBAAgB;QAChB,iBAAQ,CAAC,MAAM,CAAC,WAAW,GAAG,EAAE,CAAA;QAChC,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,IAAY;QACzB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAA;YAE/E,gBAAgB;YAChB,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAErB,mBAAmB;YACnB,MAAM,iBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEzB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;YACtC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;YAC7C,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;YAC/C,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,IAAY;QACrC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAA;YAExE,YAAY;YACZ,MAAM,iBAAiB,GAAG,oBAAS,CAAC,QAAQ,EAAE,CAAA;YAE9C,YAAY;YACZ,oBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;YAEzB,iBAAiB;YACjB,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YAEpB,eAAe;YACf,MAAM,iBAAQ,CAAC,QAAQ,CAAC,YAAG,CAAC,WAAW,EAAE,YAAG,CAAC,CAAC,CAAC,CAAA;YAC/C,MAAM,iBAAQ,CAAC,UAAU,CAAC,YAAG,CAAC,WAAW,EAAE,YAAG,CAAC,CAAC,CAAC,CAAA;YAEjD,SAAS;YACT,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAErB,YAAY;YACZ,UAAU,CAAC,GAAG,EAAE;gBACd,oBAAS,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAA;YACxC,CAAC,EAAE,IAAI,CAAC,CAAA;YAER,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAA;YACrD,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,IAAY;QAC9B,IAAI,CAAC;YACH,kBAAkB;YAClB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;gBACvB,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;gBACnD,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;YAC9C,CAAC;YAED,aAAa;YACb,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpC,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAA;gBACvE,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;YAC9C,CAAC;YAED,SAAS;YACT,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;YACzC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAElC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;YAC5C,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,IAAY;QACvC,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG,4CAA4C,CAAA;QACrE,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACpC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,IAAW;QAC7B,IAAI,CAAC;YACH,MAAM,iBAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAA;YAChC,MAAM,iBAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAA;YAClC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;YACzC,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,GAAQ;QACrB,IAAI,CAAC;YACH,MAAM,iBAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC5B,MAAM,iBAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;YAC9B,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;YACzC,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,KAAK;YACL,MAAM,iBAAQ,CAAC,QAAQ,CAAC,YAAG,CAAC,WAAW,EAAE,YAAG,CAAC,CAAC,CAAC,CAAA;YAC/C,MAAM,iBAAQ,CAAC,UAAU,CAAC,YAAG,CAAC,WAAW,EAAE,YAAG,CAAC,CAAC,CAAC,CAAA;YAEjD,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YAEpB,KAAK;YACL,MAAM,iBAAQ,CAAC,QAAQ,CAAC,YAAG,CAAC,MAAM,CAAC,CAAA;YACnC,MAAM,iBAAQ,CAAC,UAAU,CAAC,YAAG,CAAC,MAAM,CAAC,CAAA;YAErC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;YAC3C,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAG,CAAC,MAAM,CAAC,CAAA;IACxC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAG,CAAC,GAAG,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAG,CAAC,MAAM,CAAC,CAAA;IACxC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;YAElD,WAAW;YACX,MAAM,QAAQ,GAAG,UAAU,CAAA;YAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;YAElD,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAA;gBACrC,OAAO,IAAI,CAAA;YACb,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAA;gBACrC,OAAO,KAAK,CAAA;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;YAC9C,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,CAAC;YACH,iBAAiB;YACjB,OAAO;gBACL,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,gCAAgC;aACzC,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,MAAM,EAAE,aAAa;aACtB,CAAA;QACH,CAAC;IACH,CAAC;CACF;AA5ND,4CA4NC"}