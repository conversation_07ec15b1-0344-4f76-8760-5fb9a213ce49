import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Mic, Settings, Volume2 } from 'lucide-react'
import RecordingPopup from './components/RecordingPopup'
import SettingsPanel from './components/SettingsPanel'
import NotificationToast from './components/NotificationToast'
import './App.css'

interface Notification {
  id: string
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

function App() {
  const [isRecording, setIsRecording] = useState(false)
  const [recordingMode, setRecordingMode] = useState<'ai' | 'direct' | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [hotkeys, setHotkeys] = useState({
    aiMode: 'Ctrl+Shift+C',
    directMode: 'Ctrl+Shift+V'
  })
  const [notifications, setNotifications] = useState<Notification[]>([])

  // 添加通知
  const addNotification = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', duration?: number) => {
    const id = Date.now().toString()
    const notification: Notification = { id, message, type, duration }
    setNotifications(prev => [...prev, notification])
  }

  // 移除通知
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  useEffect(() => {
    // 監聽來自主程序的 IPC 訊息
    const handleIpcMessage = (event: any, type: string, data?: any) => {
      console.log('Received IPC message:', type, data)
      switch (type) {
        case 'start-recording':
          console.log('Starting recording with mode:', data.mode)
          setIsRecording(true)
          setRecordingMode(data.mode)
          break
        case 'stop-recording':
          setIsRecording(false)
          setRecordingMode(null)
          break
        case 'show-settings':
          setShowSettings(true)
          break
        case 'notification':
          addNotification(data.message, data.type, data.duration)
          break
        case 'error':
          addNotification(data.message, 'error', 5000)
          break
      }
    }

    // 監聽來自主程序的 window.postMessage 訊息（備用）
    const handleMessage = (event: any) => {
      const { type, data } = event.data
      handleIpcMessage(null, type, data)
    }

    // 嘗試使用 Electron IPC
    try {
      const { ipcRenderer } = require('electron')

      ipcRenderer.on('start-recording', (event, data) => {
        handleIpcMessage(event, 'start-recording', data)
      })

      ipcRenderer.on('stop-recording', (event) => {
        handleIpcMessage(event, 'stop-recording')
      })

      ipcRenderer.on('show-settings', (event) => {
        handleIpcMessage(event, 'show-settings')
      })

      ipcRenderer.on('notification', (event, data) => {
        handleIpcMessage(event, 'notification', data)
      })

      ipcRenderer.on('error', (event, data) => {
        handleIpcMessage(event, 'error', data)
      })

      console.log('IPC listeners registered')
    } catch (error) {
      console.log('Electron not available, using window.postMessage')
    }

    // 註冊 window.postMessage 監聽器（備用）
    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
      try {
        const { ipcRenderer } = require('electron')
        ipcRenderer.removeAllListeners('start-recording')
        ipcRenderer.removeAllListeners('stop-recording')
        ipcRenderer.removeAllListeners('show-settings')
        ipcRenderer.removeAllListeners('notification')
        ipcRenderer.removeAllListeners('error')
      } catch (error) {
        // Electron not available
      }
    }
  }, [])

  const handleSettingsChange = (settings: {
    hotkeys: { aiMode: string; directMode: string }
    apiKeys: {
      azureSpeechKey: string
      azureSpeechRegion: string
      azureOpenAIKey: string
      azureOpenAIEndpoint: string
    }
  }) => {
    setHotkeys(settings.hotkeys)

    // 發送設定變更到主程序
    if (window.require) {
      const { ipcRenderer } = window.require('electron')
      ipcRenderer.send('update-hotkeys', settings.hotkeys)
      ipcRenderer.send('update-api-config', settings.apiKeys)
    } else {
      // 備用方案：使用 postMessage
      window.postMessage({ type: 'update-hotkeys', data: settings.hotkeys }, '*')
      window.postMessage({ type: 'update-api-config', data: settings.apiKeys }, '*')
    }
  }

  return (
    <div className="app">
      <motion.div 
        className="main-container"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="header">
          <motion.div 
            className="logo"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Volume2 size={32} />
            <h1>SpeechPilot</h1>
          </motion.div>
          
          <motion.button
            className="settings-btn"
            onClick={() => setShowSettings(true)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Settings size={20} />
          </motion.button>
        </div>

        <div className="content">
          <div className="mode-cards">
            <motion.div
              className="mode-card ai-mode"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
              onClick={() => {
                // 發送開始錄音訊息到主程序
                if (window.require) {
                  const { ipcRenderer } = window.require('electron')
                  ipcRenderer.send('start-recording-request', { mode: 'ai' })
                }
              }}
            >
              <Mic size={48} />
              <h3>AI 語音助手</h3>
              <p>說出指令，AI 理解並生成回應</p>
              <div className="hotkey">{hotkeys.aiMode}</div>
            </motion.div>

            <motion.div
              className="mode-card direct-mode"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
              onClick={() => {
                // 發送開始錄音訊息到主程序
                if (window.require) {
                  const { ipcRenderer } = window.require('electron')
                  ipcRenderer.send('start-recording-request', { mode: 'direct' })
                }
              }}
            >
              <Volume2 size={48} />
              <h3>直接語音轉文字</h3>
              <p>語音直接轉換為文字輸入</p>
              <div className="hotkey">{hotkeys.directMode}</div>
            </motion.div>
          </div>

          <motion.div 
            className="status"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <p>按下快捷鍵開始使用語音功能</p>
          </motion.div>
        </div>
      </motion.div>

      {isRecording && (
        <RecordingPopup 
          mode={recordingMode!} 
          onClose={() => setIsRecording(false)}
        />
      )}

      {showSettings && (
        <SettingsPanel
          hotkeys={hotkeys}
          onSave={handleSettingsChange}
          onClose={() => setShowSettings(false)}
        />
      )}

      <NotificationToast
        notifications={notifications}
        onRemove={removeNotification}
      />
    </div>
  )
}

export default App
