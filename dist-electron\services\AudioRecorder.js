"use strict";
/**
 * AudioRecorder 服務
 * 處理音頻錄製功能，使用 Web Audio API
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioRecorder = void 0;
class AudioRecorder {
    constructor(options = {}) {
        this.mediaRecorder = null;
        this.audioStream = null;
        this.audioChunks = [];
        this.startTime = 0;
        this.options = {
            sampleRate: 16000, // Azure Speech Service 建議使用 16kHz
            channels: 1, // 單聲道
            bitDepth: 16,
            ...options
        };
    }
    /**
     * 請求麥克風權限並初始化錄音
     */
    async initialize() {
        try {
            // 請求麥克風權限
            this.audioStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: this.options.sampleRate,
                    channelCount: this.options.channels,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            console.log('Microphone access granted');
        }
        catch (error) {
            console.error('Failed to access microphone:', error);
            throw new Error('無法存取麥克風，請檢查權限設定');
        }
    }
    /**
     * 開始錄音
     */
    async startRecording() {
        if (!this.audioStream) {
            await this.initialize();
        }
        if (!this.audioStream) {
            throw new Error('音頻流未初始化');
        }
        // 清空之前的錄音數據
        this.audioChunks = [];
        // 創建 MediaRecorder
        const mimeType = this.getSupportedMimeType();
        this.mediaRecorder = new MediaRecorder(this.audioStream, {
            mimeType
        });
        // 設定事件監聽器
        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                this.audioChunks.push(event.data);
            }
        };
        this.mediaRecorder.onerror = (event) => {
            console.error('MediaRecorder error:', event);
        };
        // 開始錄音
        this.startTime = Date.now();
        this.mediaRecorder.start(100); // 每100ms收集一次數據
        console.log('Recording started');
    }
    /**
     * 停止錄音
     */
    async stopRecording() {
        return new Promise((resolve, reject) => {
            if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
                reject(new Error('錄音器未啟動'));
                return;
            }
            this.mediaRecorder.onstop = () => {
                const duration = Date.now() - this.startTime;
                const audioBlob = new Blob(this.audioChunks, {
                    type: this.getSupportedMimeType()
                });
                const recordingData = {
                    audioBlob,
                    duration,
                    sampleRate: this.options.sampleRate || 16000
                };
                console.log('Recording stopped, duration:', duration, 'ms');
                resolve(recordingData);
            };
            this.mediaRecorder.stop();
        });
    }
    /**
     * 取消錄音
     */
    cancelRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
            this.mediaRecorder.stop();
        }
        this.audioChunks = [];
        console.log('Recording cancelled');
    }
    /**
     * 釋放資源
     */
    dispose() {
        this.cancelRecording();
        if (this.audioStream) {
            this.audioStream.getTracks().forEach(track => track.stop());
            this.audioStream = null;
        }
        this.mediaRecorder = null;
        console.log('AudioRecorder disposed');
    }
    /**
     * 檢查是否正在錄音
     */
    isRecording() {
        return this.mediaRecorder?.state === 'recording';
    }
    /**
     * 獲取支援的 MIME 類型
     */
    getSupportedMimeType() {
        const types = [
            'audio/webm;codecs=opus',
            'audio/webm',
            'audio/mp4',
            'audio/wav'
        ];
        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }
        return 'audio/webm'; // 預設值
    }
    /**
     * 將 Blob 轉換為 ArrayBuffer
     */
    static async blobToArrayBuffer(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsArrayBuffer(blob);
        });
    }
    /**
     * 將 Blob 轉換為 Base64
     */
    static async blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const result = reader.result;
                // 移除 data:audio/webm;base64, 前綴
                const base64 = result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }
}
exports.AudioRecorder = AudioRecorder;
exports.default = AudioRecorder;
//# sourceMappingURL=AudioRecorder.js.map