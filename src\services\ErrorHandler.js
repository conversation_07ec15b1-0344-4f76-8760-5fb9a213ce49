/**
 * 錯誤處理服務
 * 統一處理應用程式中的錯誤和用戶反饋
 */

class ErrorHandler {
  constructor() {
    this.errorCallbacks = []
    this.notificationCallbacks = []
    console.log('<PERSON>rror Handler initialized')
  }

  /**
   * 註冊錯誤回調函數
   */
  onError(callback) {
    this.errorCallbacks.push(callback)
  }

  /**
   * 註冊通知回調函數
   */
  onNotification(callback) {
    this.notificationCallbacks.push(callback)
  }

  /**
   * 處理錯誤
   */
  handleError(error, context = '') {
    const errorInfo = this.parseError(error, context)
    
    console.error(`[${context}] Error:`, errorInfo)
    
    // 通知所有註冊的錯誤處理器
    this.errorCallbacks.forEach(callback => {
      try {
        callback(errorInfo)
      } catch (callbackError) {
        console.error('Error in error callback:', callbackError)
      }
    })
    
    return errorInfo
  }

  /**
   * 發送通知
   */
  notify(message, type = 'info', duration = 3000) {
    const notification = {
      message,
      type, // 'info', 'success', 'warning', 'error'
      duration,
      timestamp: Date.now()
    }
    
    console.log(`[${type.toUpperCase()}] ${message}`)
    
    // 通知所有註冊的通知處理器
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(notification)
      } catch (callbackError) {
        console.error('Error in notification callback:', callbackError)
      }
    })
    
    return notification
  }

  /**
   * 解析錯誤信息
   */
  parseError(error, context = '') {
    let errorInfo = {
      message: '未知錯誤',
      type: 'unknown',
      context,
      timestamp: Date.now(),
      userMessage: '發生未知錯誤，請重試'
    }

    if (typeof error === 'string') {
      errorInfo.message = error
      errorInfo.userMessage = error
      errorInfo.type = 'custom'
    } else if (error instanceof Error) {
      errorInfo.message = error.message
      errorInfo.stack = error.stack
      errorInfo.type = this.categorizeError(error)
      errorInfo.userMessage = this.getUserFriendlyMessage(error)
    } else if (error && typeof error === 'object') {
      errorInfo.message = error.message || JSON.stringify(error)
      errorInfo.userMessage = error.userMessage || this.getUserFriendlyMessage(error)
      errorInfo.type = error.type || 'object'
    }

    return errorInfo
  }

  /**
   * 分類錯誤類型
   */
  categorizeError(error) {
    const message = error.message.toLowerCase()
    
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return 'network'
    } else if (message.includes('permission') || message.includes('access')) {
      return 'permission'
    } else if (message.includes('api') || message.includes('key') || message.includes('unauthorized')) {
      return 'api'
    } else if (message.includes('audio') || message.includes('microphone') || message.includes('recording')) {
      return 'audio'
    } else if (message.includes('speech') || message.includes('recognition')) {
      return 'speech'
    } else if (message.includes('openai') || message.includes('ai')) {
      return 'ai'
    } else {
      return 'general'
    }
  }

  /**
   * 獲取用戶友好的錯誤訊息
   */
  getUserFriendlyMessage(error) {
    const message = error.message.toLowerCase()
    
    // 網路相關錯誤
    if (message.includes('network') || message.includes('fetch failed')) {
      return '網路連接失敗，請檢查網路設定'
    } else if (message.includes('timeout')) {
      return '請求超時，請重試'
    }
    
    // 權限相關錯誤
    else if (message.includes('permission') || message.includes('access')) {
      return '權限不足，請檢查應用程式權限設定'
    } else if (message.includes('microphone')) {
      return '無法存取麥克風，請檢查麥克風權限'
    }
    
    // API 相關錯誤
    else if (message.includes('api key') || message.includes('unauthorized')) {
      return 'API 金鑰無效，請檢查設定'
    } else if (message.includes('quota') || message.includes('limit')) {
      return 'API 使用額度已達上限，請稍後重試'
    }
    
    // 音頻相關錯誤
    else if (message.includes('audio') || message.includes('recording')) {
      return '音頻處理失敗，請重試'
    }
    
    // 語音識別錯誤
    else if (message.includes('speech') || message.includes('recognition')) {
      return '語音識別失敗，請重新錄音'
    }
    
    // AI 相關錯誤
    else if (message.includes('openai') || message.includes('ai')) {
      return 'AI 處理失敗，請重試'
    }
    
    // 預設訊息
    else {
      return error.message || '發生未知錯誤，請重試'
    }
  }

  /**
   * 處理 Azure Speech Service 錯誤
   */
  handleSpeechError(error) {
    return this.handleError(error, 'Azure Speech Service')
  }

  /**
   * 處理 Azure OpenAI 錯誤
   */
  handleAIError(error) {
    return this.handleError(error, 'Azure OpenAI')
  }

  /**
   * 處理音頻錄製錯誤
   */
  handleAudioError(error) {
    return this.handleError(error, 'Audio Recording')
  }

  /**
   * 處理文字輸入錯誤
   */
  handleTextInputError(error) {
    return this.handleError(error, 'Text Input')
  }

  /**
   * 處理網路錯誤
   */
  handleNetworkError(error) {
    return this.handleError(error, 'Network')
  }

  /**
   * 成功通知
   */
  success(message, duration = 2000) {
    return this.notify(message, 'success', duration)
  }

  /**
   * 警告通知
   */
  warning(message, duration = 3000) {
    return this.notify(message, 'warning', duration)
  }

  /**
   * 錯誤通知
   */
  error(message, duration = 5000) {
    return this.notify(message, 'error', duration)
  }

  /**
   * 信息通知
   */
  info(message, duration = 3000) {
    return this.notify(message, 'info', duration)
  }

  /**
   * 清除所有回調
   */
  dispose() {
    this.errorCallbacks = []
    this.notificationCallbacks = []
    console.log('Error Handler disposed')
  }
}

// 創建全域實例
const errorHandler = new ErrorHandler()

module.exports = errorHandler
