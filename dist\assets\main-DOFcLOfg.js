var Nm=Object.defineProperty;var jm=(e,t,n)=>t in e?Nm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var dn=(e,t,n)=>jm(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function _m(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Hf={exports:{}},ks={},Gf={exports:{}},_={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qr=Symbol.for("react.element"),Im=Symbol.for("react.portal"),zm=Symbol.for("react.fragment"),Fm=Symbol.for("react.strict_mode"),Om=Symbol.for("react.profiler"),Bm=Symbol.for("react.provider"),Um=Symbol.for("react.context"),$m=Symbol.for("react.forward_ref"),Wm=Symbol.for("react.suspense"),Km=Symbol.for("react.memo"),Hm=Symbol.for("react.lazy"),wu=Symbol.iterator;function Gm(e){return e===null||typeof e!="object"?null:(e=wu&&e[wu]||e["@@iterator"],typeof e=="function"?e:null)}var Qf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Xf=Object.assign,Yf={};function Xn(e,t,n){this.props=e,this.context=t,this.refs=Yf,this.updater=n||Qf}Xn.prototype.isReactComponent={};Xn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Xn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Zf(){}Zf.prototype=Xn.prototype;function $l(e,t,n){this.props=e,this.context=t,this.refs=Yf,this.updater=n||Qf}var Wl=$l.prototype=new Zf;Wl.constructor=$l;Xf(Wl,Xn.prototype);Wl.isPureReactComponent=!0;var Su=Array.isArray,qf=Object.prototype.hasOwnProperty,Kl={current:null},Jf={key:!0,ref:!0,__self:!0,__source:!0};function bf(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)qf.call(t,r)&&!Jf.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:qr,type:e,key:s,ref:o,props:i,_owner:Kl.current}}function Qm(e,t){return{$$typeof:qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Hl(e){return typeof e=="object"&&e!==null&&e.$$typeof===qr}function Xm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var xu=/\/+/g;function Hs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Xm(""+e.key):t.toString(36)}function Di(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case qr:case Im:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Hs(o,0):r,Su(i)?(n="",e!=null&&(n=e.replace(xu,"$&/")+"/"),Di(i,t,n,"",function(u){return u})):i!=null&&(Hl(i)&&(i=Qm(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(xu,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",Su(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+Hs(s,l);o+=Di(s,t,n,a,i)}else if(a=Gm(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+Hs(s,l++),o+=Di(s,t,n,a,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function ui(e,t,n){if(e==null)return e;var r=[],i=0;return Di(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Ym(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var we={current:null},Li={transition:null},Zm={ReactCurrentDispatcher:we,ReactCurrentBatchConfig:Li,ReactCurrentOwner:Kl};function ed(){throw Error("act(...) is not supported in production builds of React.")}_.Children={map:ui,forEach:function(e,t,n){ui(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ui(e,function(){t++}),t},toArray:function(e){return ui(e,function(t){return t})||[]},only:function(e){if(!Hl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};_.Component=Xn;_.Fragment=zm;_.Profiler=Om;_.PureComponent=$l;_.StrictMode=Fm;_.Suspense=Wm;_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zm;_.act=ed;_.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Xf({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=Kl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)qf.call(t,a)&&!Jf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:qr,type:e.type,key:i,ref:s,props:r,_owner:o}};_.createContext=function(e){return e={$$typeof:Um,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Bm,_context:e},e.Consumer=e};_.createElement=bf;_.createFactory=function(e){var t=bf.bind(null,e);return t.type=e,t};_.createRef=function(){return{current:null}};_.forwardRef=function(e){return{$$typeof:$m,render:e}};_.isValidElement=Hl;_.lazy=function(e){return{$$typeof:Hm,_payload:{_status:-1,_result:e},_init:Ym}};_.memo=function(e,t){return{$$typeof:Km,type:e,compare:t===void 0?null:t}};_.startTransition=function(e){var t=Li.transition;Li.transition={};try{e()}finally{Li.transition=t}};_.unstable_act=ed;_.useCallback=function(e,t){return we.current.useCallback(e,t)};_.useContext=function(e){return we.current.useContext(e)};_.useDebugValue=function(){};_.useDeferredValue=function(e){return we.current.useDeferredValue(e)};_.useEffect=function(e,t){return we.current.useEffect(e,t)};_.useId=function(){return we.current.useId()};_.useImperativeHandle=function(e,t,n){return we.current.useImperativeHandle(e,t,n)};_.useInsertionEffect=function(e,t){return we.current.useInsertionEffect(e,t)};_.useLayoutEffect=function(e,t){return we.current.useLayoutEffect(e,t)};_.useMemo=function(e,t){return we.current.useMemo(e,t)};_.useReducer=function(e,t,n){return we.current.useReducer(e,t,n)};_.useRef=function(e){return we.current.useRef(e)};_.useState=function(e){return we.current.useState(e)};_.useSyncExternalStore=function(e,t,n){return we.current.useSyncExternalStore(e,t,n)};_.useTransition=function(){return we.current.useTransition()};_.version="18.3.1";Gf.exports=_;var E=Gf.exports;const qm=_m(E);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jm=E,bm=Symbol.for("react.element"),eg=Symbol.for("react.fragment"),tg=Object.prototype.hasOwnProperty,ng=Jm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,rg={key:!0,ref:!0,__self:!0,__source:!0};function td(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)tg.call(t,r)&&!rg.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:bm,type:e,key:s,ref:o,props:i,_owner:ng.current}}ks.Fragment=eg;ks.jsx=td;ks.jsxs=td;Hf.exports=ks;var k=Hf.exports,Vo={},nd={exports:{}},Ve={},rd={exports:{}},id={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(R,V){var N=R.length;R.push(V);e:for(;0<N;){var Y=N-1>>>1,re=R[Y];if(0<i(re,V))R[Y]=V,R[N]=re,N=Y;else break e}}function n(R){return R.length===0?null:R[0]}function r(R){if(R.length===0)return null;var V=R[0],N=R.pop();if(N!==V){R[0]=N;e:for(var Y=0,re=R.length,li=re>>>1;Y<li;){var Wt=2*(Y+1)-1,Ks=R[Wt],Kt=Wt+1,ai=R[Kt];if(0>i(Ks,N))Kt<re&&0>i(ai,Ks)?(R[Y]=ai,R[Kt]=N,Y=Kt):(R[Y]=Ks,R[Wt]=N,Y=Wt);else if(Kt<re&&0>i(ai,N))R[Y]=ai,R[Kt]=N,Y=Kt;else break e}}return V}function i(R,V){var N=R.sortIndex-V.sortIndex;return N!==0?N:R.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var a=[],u=[],c=1,f=null,d=3,g=!1,v=!1,y=!1,S=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(R){for(var V=n(u);V!==null;){if(V.callback===null)r(u);else if(V.startTime<=R)r(u),V.sortIndex=V.expirationTime,t(a,V);else break;V=n(u)}}function w(R){if(y=!1,m(R),!v)if(n(a)!==null)v=!0,oi(x);else{var V=n(u);V!==null&&b(w,V.startTime-R)}}function x(R,V){v=!1,y&&(y=!1,h(P),P=-1),g=!0;var N=d;try{for(m(V),f=n(a);f!==null&&(!(f.expirationTime>V)||R&&!ne());){var Y=f.callback;if(typeof Y=="function"){f.callback=null,d=f.priorityLevel;var re=Y(f.expirationTime<=V);V=e.unstable_now(),typeof re=="function"?f.callback=re:f===n(a)&&r(a),m(V)}else r(a);f=n(a)}if(f!==null)var li=!0;else{var Wt=n(u);Wt!==null&&b(w,Wt.startTime-V),li=!1}return li}finally{f=null,d=N,g=!1}}var C=!1,A=null,P=-1,j=5,L=-1;function ne(){return!(e.unstable_now()-L<j)}function yt(){if(A!==null){var R=e.unstable_now();L=R;var V=!0;try{V=A(!0,R)}finally{V?$t():(C=!1,A=null)}}else C=!1}var $t;if(typeof p=="function")$t=function(){p(yt)};else if(typeof MessageChannel<"u"){var bn=new MessageChannel,vu=bn.port2;bn.port1.onmessage=yt,$t=function(){vu.postMessage(null)}}else $t=function(){S(yt,0)};function oi(R){A=R,C||(C=!0,$t())}function b(R,V){P=S(function(){R(e.unstable_now())},V)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(R){R.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,oi(x))},e.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<R?Math.floor(1e3/R):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(R){switch(d){case 1:case 2:case 3:var V=3;break;default:V=d}var N=d;d=V;try{return R()}finally{d=N}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(R,V){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var N=d;d=R;try{return V()}finally{d=N}},e.unstable_scheduleCallback=function(R,V,N){var Y=e.unstable_now();switch(typeof N=="object"&&N!==null?(N=N.delay,N=typeof N=="number"&&0<N?Y+N:Y):N=Y,R){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=N+re,R={id:c++,callback:V,priorityLevel:R,startTime:N,expirationTime:re,sortIndex:-1},N>Y?(R.sortIndex=N,t(u,R),n(a)===null&&R===n(u)&&(y?(h(P),P=-1):y=!0,b(w,N-Y))):(R.sortIndex=re,t(a,R),v||g||(v=!0,oi(x))),R},e.unstable_shouldYield=ne,e.unstable_wrapCallback=function(R){var V=d;return function(){var N=d;d=V;try{return R.apply(this,arguments)}finally{d=N}}}})(id);rd.exports=id;var ig=rd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sg=E,De=ig;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var sd=new Set,Mr={};function un(e,t){Fn(e,t),Fn(e+"Capture",t)}function Fn(e,t){for(Mr[e]=t,e=0;e<t.length;e++)sd.add(t[e])}var ft=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),No=Object.prototype.hasOwnProperty,og=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ku={},Pu={};function lg(e){return No.call(Pu,e)?!0:No.call(ku,e)?!1:og.test(e)?Pu[e]=!0:(ku[e]=!0,!1)}function ag(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ug(e,t,n,r){if(t===null||typeof t>"u"||ag(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Se(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new Se(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new Se(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new Se(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new Se(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new Se(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new Se(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new Se(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new Se(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new Se(e,5,!1,e.toLowerCase(),null,!1,!1)});var Gl=/[\-:]([a-z])/g;function Ql(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Gl,Ql);ce[t]=new Se(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Gl,Ql);ce[t]=new Se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Gl,Ql);ce[t]=new Se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new Se(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new Se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new Se(e,1,!1,e.toLowerCase(),null,!0,!0)});function Xl(e,t,n,r){var i=ce.hasOwnProperty(t)?ce[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ug(t,n,i,r)&&(n=null),r||i===null?lg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var gt=sg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ci=Symbol.for("react.element"),pn=Symbol.for("react.portal"),mn=Symbol.for("react.fragment"),Yl=Symbol.for("react.strict_mode"),jo=Symbol.for("react.profiler"),od=Symbol.for("react.provider"),ld=Symbol.for("react.context"),Zl=Symbol.for("react.forward_ref"),_o=Symbol.for("react.suspense"),Io=Symbol.for("react.suspense_list"),ql=Symbol.for("react.memo"),St=Symbol.for("react.lazy"),ad=Symbol.for("react.offscreen"),Tu=Symbol.iterator;function er(e){return e===null||typeof e!="object"?null:(e=Tu&&e[Tu]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,Gs;function ur(e){if(Gs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Gs=t&&t[1]||""}return`
`+Gs+e}var Qs=!1;function Xs(e,t){if(!e||Qs)return"";Qs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,l=s.length-1;1<=o&&0<=l&&i[o]!==s[l];)l--;for(;1<=o&&0<=l;o--,l--)if(i[o]!==s[l]){if(o!==1||l!==1)do if(o--,l--,0>l||i[o]!==s[l]){var a=`
`+i[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=l);break}}}finally{Qs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ur(e):""}function cg(e){switch(e.tag){case 5:return ur(e.type);case 16:return ur("Lazy");case 13:return ur("Suspense");case 19:return ur("SuspenseList");case 0:case 2:case 15:return e=Xs(e.type,!1),e;case 11:return e=Xs(e.type.render,!1),e;case 1:return e=Xs(e.type,!0),e;default:return""}}function zo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case mn:return"Fragment";case pn:return"Portal";case jo:return"Profiler";case Yl:return"StrictMode";case _o:return"Suspense";case Io:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ld:return(e.displayName||"Context")+".Consumer";case od:return(e._context.displayName||"Context")+".Provider";case Zl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ql:return t=e.displayName||null,t!==null?t:zo(e.type)||"Memo";case St:t=e._payload,e=e._init;try{return zo(e(t))}catch{}}return null}function fg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return zo(t);case 8:return t===Yl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function jt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ud(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function dg(e){var t=ud(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function fi(e){e._valueTracker||(e._valueTracker=dg(e))}function cd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ud(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Hi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Fo(e,t){var n=t.checked;return G({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Cu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=jt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function fd(e,t){t=t.checked,t!=null&&Xl(e,"checked",t,!1)}function Oo(e,t){fd(e,t);var n=jt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Bo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Bo(e,t.type,jt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Eu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Bo(e,t,n){(t!=="number"||Hi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var cr=Array.isArray;function Vn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+jt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Uo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return G({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Au(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(cr(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:jt(n)}}function dd(e,t){var n=jt(t.value),r=jt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ru(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function hd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function $o(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?hd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var di,pd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(di=di||document.createElement("div"),di.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=di.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Dr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var gr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hg=["Webkit","ms","Moz","O"];Object.keys(gr).forEach(function(e){hg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),gr[t]=gr[e]})});function md(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||gr.hasOwnProperty(e)&&gr[e]?(""+t).trim():t+"px"}function gd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=md(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var pg=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wo(e,t){if(t){if(pg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function Ko(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ho=null;function Jl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Go=null,Nn=null,jn=null;function Mu(e){if(e=ei(e)){if(typeof Go!="function")throw Error(T(280));var t=e.stateNode;t&&(t=As(t),Go(e.stateNode,e.type,t))}}function yd(e){Nn?jn?jn.push(e):jn=[e]:Nn=e}function vd(){if(Nn){var e=Nn,t=jn;if(jn=Nn=null,Mu(e),t)for(e=0;e<t.length;e++)Mu(t[e])}}function wd(e,t){return e(t)}function Sd(){}var Ys=!1;function xd(e,t,n){if(Ys)return e(t,n);Ys=!0;try{return wd(e,t,n)}finally{Ys=!1,(Nn!==null||jn!==null)&&(Sd(),vd())}}function Lr(e,t){var n=e.stateNode;if(n===null)return null;var r=As(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var Qo=!1;if(ft)try{var tr={};Object.defineProperty(tr,"passive",{get:function(){Qo=!0}}),window.addEventListener("test",tr,tr),window.removeEventListener("test",tr,tr)}catch{Qo=!1}function mg(e,t,n,r,i,s,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var yr=!1,Gi=null,Qi=!1,Xo=null,gg={onError:function(e){yr=!0,Gi=e}};function yg(e,t,n,r,i,s,o,l,a){yr=!1,Gi=null,mg.apply(gg,arguments)}function vg(e,t,n,r,i,s,o,l,a){if(yg.apply(this,arguments),yr){if(yr){var u=Gi;yr=!1,Gi=null}else throw Error(T(198));Qi||(Qi=!0,Xo=u)}}function cn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function kd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Du(e){if(cn(e)!==e)throw Error(T(188))}function wg(e){var t=e.alternate;if(!t){if(t=cn(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Du(i),e;if(s===r)return Du(i),t;s=s.sibling}throw Error(T(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function Pd(e){return e=wg(e),e!==null?Td(e):null}function Td(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Td(e);if(t!==null)return t;e=e.sibling}return null}var Cd=De.unstable_scheduleCallback,Lu=De.unstable_cancelCallback,Sg=De.unstable_shouldYield,xg=De.unstable_requestPaint,q=De.unstable_now,kg=De.unstable_getCurrentPriorityLevel,bl=De.unstable_ImmediatePriority,Ed=De.unstable_UserBlockingPriority,Xi=De.unstable_NormalPriority,Pg=De.unstable_LowPriority,Ad=De.unstable_IdlePriority,Ps=null,et=null;function Tg(e){if(et&&typeof et.onCommitFiberRoot=="function")try{et.onCommitFiberRoot(Ps,e,void 0,(e.current.flags&128)===128)}catch{}}var Xe=Math.clz32?Math.clz32:Ag,Cg=Math.log,Eg=Math.LN2;function Ag(e){return e>>>=0,e===0?32:31-(Cg(e)/Eg|0)|0}var hi=64,pi=4194304;function fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Yi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~i;l!==0?r=fr(l):(s&=o,s!==0&&(r=fr(s)))}else o=n&~i,o!==0?r=fr(o):s!==0&&(r=fr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Xe(t),i=1<<n,r|=e[n],t&=~i;return r}function Rg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Mg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-Xe(s),l=1<<o,a=i[o];a===-1?(!(l&n)||l&r)&&(i[o]=Rg(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function Yo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Rd(){var e=hi;return hi<<=1,!(hi&4194240)&&(hi=64),e}function Zs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Jr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xe(t),e[t]=n}function Dg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Xe(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function ea(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Xe(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var z=0;function Md(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Dd,ta,Ld,Vd,Nd,Zo=!1,mi=[],Et=null,At=null,Rt=null,Vr=new Map,Nr=new Map,kt=[],Lg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Vu(e,t){switch(e){case"focusin":case"focusout":Et=null;break;case"dragenter":case"dragleave":At=null;break;case"mouseover":case"mouseout":Rt=null;break;case"pointerover":case"pointerout":Vr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Nr.delete(t.pointerId)}}function nr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=ei(t),t!==null&&ta(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Vg(e,t,n,r,i){switch(t){case"focusin":return Et=nr(Et,e,t,n,r,i),!0;case"dragenter":return At=nr(At,e,t,n,r,i),!0;case"mouseover":return Rt=nr(Rt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Vr.set(s,nr(Vr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Nr.set(s,nr(Nr.get(s)||null,e,t,n,r,i)),!0}return!1}function jd(e){var t=Zt(e.target);if(t!==null){var n=cn(t);if(n!==null){if(t=n.tag,t===13){if(t=kd(n),t!==null){e.blockedOn=t,Nd(e.priority,function(){Ld(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Vi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=qo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ho=r,n.target.dispatchEvent(r),Ho=null}else return t=ei(n),t!==null&&ta(t),e.blockedOn=n,!1;t.shift()}return!0}function Nu(e,t,n){Vi(e)&&n.delete(t)}function Ng(){Zo=!1,Et!==null&&Vi(Et)&&(Et=null),At!==null&&Vi(At)&&(At=null),Rt!==null&&Vi(Rt)&&(Rt=null),Vr.forEach(Nu),Nr.forEach(Nu)}function rr(e,t){e.blockedOn===t&&(e.blockedOn=null,Zo||(Zo=!0,De.unstable_scheduleCallback(De.unstable_NormalPriority,Ng)))}function jr(e){function t(i){return rr(i,e)}if(0<mi.length){rr(mi[0],e);for(var n=1;n<mi.length;n++){var r=mi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Et!==null&&rr(Et,e),At!==null&&rr(At,e),Rt!==null&&rr(Rt,e),Vr.forEach(t),Nr.forEach(t),n=0;n<kt.length;n++)r=kt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<kt.length&&(n=kt[0],n.blockedOn===null);)jd(n),n.blockedOn===null&&kt.shift()}var _n=gt.ReactCurrentBatchConfig,Zi=!0;function jg(e,t,n,r){var i=z,s=_n.transition;_n.transition=null;try{z=1,na(e,t,n,r)}finally{z=i,_n.transition=s}}function _g(e,t,n,r){var i=z,s=_n.transition;_n.transition=null;try{z=4,na(e,t,n,r)}finally{z=i,_n.transition=s}}function na(e,t,n,r){if(Zi){var i=qo(e,t,n,r);if(i===null)oo(e,t,r,qi,n),Vu(e,r);else if(Vg(i,e,t,n,r))r.stopPropagation();else if(Vu(e,r),t&4&&-1<Lg.indexOf(e)){for(;i!==null;){var s=ei(i);if(s!==null&&Dd(s),s=qo(e,t,n,r),s===null&&oo(e,t,r,qi,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else oo(e,t,r,null,n)}}var qi=null;function qo(e,t,n,r){if(qi=null,e=Jl(r),e=Zt(e),e!==null)if(t=cn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=kd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return qi=e,null}function _d(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(kg()){case bl:return 1;case Ed:return 4;case Xi:case Pg:return 16;case Ad:return 536870912;default:return 16}default:return 16}}var Tt=null,ra=null,Ni=null;function Id(){if(Ni)return Ni;var e,t=ra,n=t.length,r,i="value"in Tt?Tt.value:Tt.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return Ni=i.slice(e,1<r?1-r:void 0)}function ji(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function gi(){return!0}function ju(){return!1}function Ne(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?gi:ju,this.isPropagationStopped=ju,this}return G(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=gi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=gi)},persist:function(){},isPersistent:gi}),t}var Yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ia=Ne(Yn),br=G({},Yn,{view:0,detail:0}),Ig=Ne(br),qs,Js,ir,Ts=G({},br,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:sa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ir&&(ir&&e.type==="mousemove"?(qs=e.screenX-ir.screenX,Js=e.screenY-ir.screenY):Js=qs=0,ir=e),qs)},movementY:function(e){return"movementY"in e?e.movementY:Js}}),_u=Ne(Ts),zg=G({},Ts,{dataTransfer:0}),Fg=Ne(zg),Og=G({},br,{relatedTarget:0}),bs=Ne(Og),Bg=G({},Yn,{animationName:0,elapsedTime:0,pseudoElement:0}),Ug=Ne(Bg),$g=G({},Yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Wg=Ne($g),Kg=G({},Yn,{data:0}),Iu=Ne(Kg),Hg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Gg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Qg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Xg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Qg[e])?!!t[e]:!1}function sa(){return Xg}var Yg=G({},br,{key:function(e){if(e.key){var t=Hg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ji(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Gg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:sa,charCode:function(e){return e.type==="keypress"?ji(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ji(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Zg=Ne(Yg),qg=G({},Ts,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),zu=Ne(qg),Jg=G({},br,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:sa}),bg=Ne(Jg),ey=G({},Yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),ty=Ne(ey),ny=G({},Ts,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ry=Ne(ny),iy=[9,13,27,32],oa=ft&&"CompositionEvent"in window,vr=null;ft&&"documentMode"in document&&(vr=document.documentMode);var sy=ft&&"TextEvent"in window&&!vr,zd=ft&&(!oa||vr&&8<vr&&11>=vr),Fu=" ",Ou=!1;function Fd(e,t){switch(e){case"keyup":return iy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Od(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var gn=!1;function oy(e,t){switch(e){case"compositionend":return Od(t);case"keypress":return t.which!==32?null:(Ou=!0,Fu);case"textInput":return e=t.data,e===Fu&&Ou?null:e;default:return null}}function ly(e,t){if(gn)return e==="compositionend"||!oa&&Fd(e,t)?(e=Id(),Ni=ra=Tt=null,gn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return zd&&t.locale!=="ko"?null:t.data;default:return null}}var ay={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ay[e.type]:t==="textarea"}function Bd(e,t,n,r){yd(r),t=Ji(t,"onChange"),0<t.length&&(n=new ia("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var wr=null,_r=null;function uy(e){qd(e,0)}function Cs(e){var t=wn(e);if(cd(t))return e}function cy(e,t){if(e==="change")return t}var Ud=!1;if(ft){var eo;if(ft){var to="oninput"in document;if(!to){var Uu=document.createElement("div");Uu.setAttribute("oninput","return;"),to=typeof Uu.oninput=="function"}eo=to}else eo=!1;Ud=eo&&(!document.documentMode||9<document.documentMode)}function $u(){wr&&(wr.detachEvent("onpropertychange",$d),_r=wr=null)}function $d(e){if(e.propertyName==="value"&&Cs(_r)){var t=[];Bd(t,_r,e,Jl(e)),xd(uy,t)}}function fy(e,t,n){e==="focusin"?($u(),wr=t,_r=n,wr.attachEvent("onpropertychange",$d)):e==="focusout"&&$u()}function dy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Cs(_r)}function hy(e,t){if(e==="click")return Cs(t)}function py(e,t){if(e==="input"||e==="change")return Cs(t)}function my(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:my;function Ir(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!No.call(t,i)||!Ze(e[i],t[i]))return!1}return!0}function Wu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ku(e,t){var n=Wu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wu(n)}}function Wd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Wd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Kd(){for(var e=window,t=Hi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Hi(e.document)}return t}function la(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function gy(e){var t=Kd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Wd(n.ownerDocument.documentElement,n)){if(r!==null&&la(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Ku(n,s);var o=Ku(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var yy=ft&&"documentMode"in document&&11>=document.documentMode,yn=null,Jo=null,Sr=null,bo=!1;function Hu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;bo||yn==null||yn!==Hi(r)||(r=yn,"selectionStart"in r&&la(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Sr&&Ir(Sr,r)||(Sr=r,r=Ji(Jo,"onSelect"),0<r.length&&(t=new ia("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=yn)))}function yi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var vn={animationend:yi("Animation","AnimationEnd"),animationiteration:yi("Animation","AnimationIteration"),animationstart:yi("Animation","AnimationStart"),transitionend:yi("Transition","TransitionEnd")},no={},Hd={};ft&&(Hd=document.createElement("div").style,"AnimationEvent"in window||(delete vn.animationend.animation,delete vn.animationiteration.animation,delete vn.animationstart.animation),"TransitionEvent"in window||delete vn.transitionend.transition);function Es(e){if(no[e])return no[e];if(!vn[e])return e;var t=vn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Hd)return no[e]=t[n];return e}var Gd=Es("animationend"),Qd=Es("animationiteration"),Xd=Es("animationstart"),Yd=Es("transitionend"),Zd=new Map,Gu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ft(e,t){Zd.set(e,t),un(t,[e])}for(var ro=0;ro<Gu.length;ro++){var io=Gu[ro],vy=io.toLowerCase(),wy=io[0].toUpperCase()+io.slice(1);Ft(vy,"on"+wy)}Ft(Gd,"onAnimationEnd");Ft(Qd,"onAnimationIteration");Ft(Xd,"onAnimationStart");Ft("dblclick","onDoubleClick");Ft("focusin","onFocus");Ft("focusout","onBlur");Ft(Yd,"onTransitionEnd");Fn("onMouseEnter",["mouseout","mouseover"]);Fn("onMouseLeave",["mouseout","mouseover"]);Fn("onPointerEnter",["pointerout","pointerover"]);Fn("onPointerLeave",["pointerout","pointerover"]);un("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));un("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));un("onBeforeInput",["compositionend","keypress","textInput","paste"]);un("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));un("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));un("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Sy=new Set("cancel close invalid load scroll toggle".split(" ").concat(dr));function Qu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,vg(r,t,void 0,e),e.currentTarget=null}function qd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&i.isPropagationStopped())break e;Qu(i,l,u),s=a}else for(o=0;o<r.length;o++){if(l=r[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&i.isPropagationStopped())break e;Qu(i,l,u),s=a}}}if(Qi)throw e=Xo,Qi=!1,Xo=null,e}function O(e,t){var n=t[il];n===void 0&&(n=t[il]=new Set);var r=e+"__bubble";n.has(r)||(Jd(t,e,2,!1),n.add(r))}function so(e,t,n){var r=0;t&&(r|=4),Jd(n,e,r,t)}var vi="_reactListening"+Math.random().toString(36).slice(2);function zr(e){if(!e[vi]){e[vi]=!0,sd.forEach(function(n){n!=="selectionchange"&&(Sy.has(n)||so(n,!1,e),so(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[vi]||(t[vi]=!0,so("selectionchange",!1,t))}}function Jd(e,t,n,r){switch(_d(t)){case 1:var i=jg;break;case 4:i=_g;break;default:i=na}n=i.bind(null,t,n,e),i=void 0,!Qo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function oo(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;o=o.return}for(;l!==null;){if(o=Zt(l),o===null)return;if(a=o.tag,a===5||a===6){r=s=o;continue e}l=l.parentNode}}r=r.return}xd(function(){var u=s,c=Jl(n),f=[];e:{var d=Zd.get(e);if(d!==void 0){var g=ia,v=e;switch(e){case"keypress":if(ji(n)===0)break e;case"keydown":case"keyup":g=Zg;break;case"focusin":v="focus",g=bs;break;case"focusout":v="blur",g=bs;break;case"beforeblur":case"afterblur":g=bs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=_u;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Fg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=bg;break;case Gd:case Qd:case Xd:g=Ug;break;case Yd:g=ty;break;case"scroll":g=Ig;break;case"wheel":g=ry;break;case"copy":case"cut":case"paste":g=Wg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=zu}var y=(t&4)!==0,S=!y&&e==="scroll",h=y?d!==null?d+"Capture":null:d;y=[];for(var p=u,m;p!==null;){m=p;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,h!==null&&(w=Lr(p,h),w!=null&&y.push(Fr(p,w,m)))),S)break;p=p.return}0<y.length&&(d=new g(d,v,null,n,c),f.push({event:d,listeners:y}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",d&&n!==Ho&&(v=n.relatedTarget||n.fromElement)&&(Zt(v)||v[dt]))break e;if((g||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?Zt(v):null,v!==null&&(S=cn(v),v!==S||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(y=_u,w="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=zu,w="onPointerLeave",h="onPointerEnter",p="pointer"),S=g==null?d:wn(g),m=v==null?d:wn(v),d=new y(w,p+"leave",g,n,c),d.target=S,d.relatedTarget=m,w=null,Zt(c)===u&&(y=new y(h,p+"enter",v,n,c),y.target=m,y.relatedTarget=S,w=y),S=w,g&&v)t:{for(y=g,h=v,p=0,m=y;m;m=hn(m))p++;for(m=0,w=h;w;w=hn(w))m++;for(;0<p-m;)y=hn(y),p--;for(;0<m-p;)h=hn(h),m--;for(;p--;){if(y===h||h!==null&&y===h.alternate)break t;y=hn(y),h=hn(h)}y=null}else y=null;g!==null&&Xu(f,d,g,y,!1),v!==null&&S!==null&&Xu(f,S,v,y,!0)}}e:{if(d=u?wn(u):window,g=d.nodeName&&d.nodeName.toLowerCase(),g==="select"||g==="input"&&d.type==="file")var x=cy;else if(Bu(d))if(Ud)x=py;else{x=dy;var C=fy}else(g=d.nodeName)&&g.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(x=hy);if(x&&(x=x(e,u))){Bd(f,x,n,c);break e}C&&C(e,d,u),e==="focusout"&&(C=d._wrapperState)&&C.controlled&&d.type==="number"&&Bo(d,"number",d.value)}switch(C=u?wn(u):window,e){case"focusin":(Bu(C)||C.contentEditable==="true")&&(yn=C,Jo=u,Sr=null);break;case"focusout":Sr=Jo=yn=null;break;case"mousedown":bo=!0;break;case"contextmenu":case"mouseup":case"dragend":bo=!1,Hu(f,n,c);break;case"selectionchange":if(yy)break;case"keydown":case"keyup":Hu(f,n,c)}var A;if(oa)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else gn?Fd(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(zd&&n.locale!=="ko"&&(gn||P!=="onCompositionStart"?P==="onCompositionEnd"&&gn&&(A=Id()):(Tt=c,ra="value"in Tt?Tt.value:Tt.textContent,gn=!0)),C=Ji(u,P),0<C.length&&(P=new Iu(P,e,null,n,c),f.push({event:P,listeners:C}),A?P.data=A:(A=Od(n),A!==null&&(P.data=A)))),(A=sy?oy(e,n):ly(e,n))&&(u=Ji(u,"onBeforeInput"),0<u.length&&(c=new Iu("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=A))}qd(f,t)})}function Fr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ji(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Lr(e,n),s!=null&&r.unshift(Fr(e,s,i)),s=Lr(e,t),s!=null&&r.push(Fr(e,s,i))),e=e.return}return r}function hn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Xu(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Lr(n,s),a!=null&&o.unshift(Fr(n,a,l))):i||(a=Lr(n,s),a!=null&&o.push(Fr(n,a,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var xy=/\r\n?/g,ky=/\u0000|\uFFFD/g;function Yu(e){return(typeof e=="string"?e:""+e).replace(xy,`
`).replace(ky,"")}function wi(e,t,n){if(t=Yu(t),Yu(e)!==t&&n)throw Error(T(425))}function bi(){}var el=null,tl=null;function nl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var rl=typeof setTimeout=="function"?setTimeout:void 0,Py=typeof clearTimeout=="function"?clearTimeout:void 0,Zu=typeof Promise=="function"?Promise:void 0,Ty=typeof queueMicrotask=="function"?queueMicrotask:typeof Zu<"u"?function(e){return Zu.resolve(null).then(e).catch(Cy)}:rl;function Cy(e){setTimeout(function(){throw e})}function lo(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),jr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);jr(t)}function Mt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function qu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Zn=Math.random().toString(36).slice(2),be="__reactFiber$"+Zn,Or="__reactProps$"+Zn,dt="__reactContainer$"+Zn,il="__reactEvents$"+Zn,Ey="__reactListeners$"+Zn,Ay="__reactHandles$"+Zn;function Zt(e){var t=e[be];if(t)return t;for(var n=e.parentNode;n;){if(t=n[dt]||n[be]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qu(e);e!==null;){if(n=e[be])return n;e=qu(e)}return t}e=n,n=e.parentNode}return null}function ei(e){return e=e[be]||e[dt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function wn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function As(e){return e[Or]||null}var sl=[],Sn=-1;function Ot(e){return{current:e}}function B(e){0>Sn||(e.current=sl[Sn],sl[Sn]=null,Sn--)}function F(e,t){Sn++,sl[Sn]=e.current,e.current=t}var _t={},ge=Ot(_t),Pe=Ot(!1),rn=_t;function On(e,t){var n=e.type.contextTypes;if(!n)return _t;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Te(e){return e=e.childContextTypes,e!=null}function es(){B(Pe),B(ge)}function Ju(e,t,n){if(ge.current!==_t)throw Error(T(168));F(ge,t),F(Pe,n)}function bd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(T(108,fg(e)||"Unknown",i));return G({},n,r)}function ts(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||_t,rn=ge.current,F(ge,e),F(Pe,Pe.current),!0}function bu(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=bd(e,t,rn),r.__reactInternalMemoizedMergedChildContext=e,B(Pe),B(ge),F(ge,e)):B(Pe),F(Pe,n)}var st=null,Rs=!1,ao=!1;function eh(e){st===null?st=[e]:st.push(e)}function Ry(e){Rs=!0,eh(e)}function Bt(){if(!ao&&st!==null){ao=!0;var e=0,t=z;try{var n=st;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}st=null,Rs=!1}catch(i){throw st!==null&&(st=st.slice(e+1)),Cd(bl,Bt),i}finally{z=t,ao=!1}}return null}var xn=[],kn=0,ns=null,rs=0,Ie=[],ze=0,sn=null,ot=1,lt="";function Gt(e,t){xn[kn++]=rs,xn[kn++]=ns,ns=e,rs=t}function th(e,t,n){Ie[ze++]=ot,Ie[ze++]=lt,Ie[ze++]=sn,sn=e;var r=ot;e=lt;var i=32-Xe(r)-1;r&=~(1<<i),n+=1;var s=32-Xe(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,ot=1<<32-Xe(t)+i|n<<i|r,lt=s+e}else ot=1<<s|n<<i|r,lt=e}function aa(e){e.return!==null&&(Gt(e,1),th(e,1,0))}function ua(e){for(;e===ns;)ns=xn[--kn],xn[kn]=null,rs=xn[--kn],xn[kn]=null;for(;e===sn;)sn=Ie[--ze],Ie[ze]=null,lt=Ie[--ze],Ie[ze]=null,ot=Ie[--ze],Ie[ze]=null}var Re=null,Ae=null,$=!1,Qe=null;function nh(e,t){var n=Fe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ec(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Re=e,Ae=Mt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Re=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=sn!==null?{id:ot,overflow:lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Fe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Re=e,Ae=null,!0):!1;default:return!1}}function ol(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ll(e){if($){var t=Ae;if(t){var n=t;if(!ec(e,t)){if(ol(e))throw Error(T(418));t=Mt(n.nextSibling);var r=Re;t&&ec(e,t)?nh(r,n):(e.flags=e.flags&-4097|2,$=!1,Re=e)}}else{if(ol(e))throw Error(T(418));e.flags=e.flags&-4097|2,$=!1,Re=e}}}function tc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Re=e}function Si(e){if(e!==Re)return!1;if(!$)return tc(e),$=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!nl(e.type,e.memoizedProps)),t&&(t=Ae)){if(ol(e))throw rh(),Error(T(418));for(;t;)nh(e,t),t=Mt(t.nextSibling)}if(tc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ae=Mt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=Re?Mt(e.stateNode.nextSibling):null;return!0}function rh(){for(var e=Ae;e;)e=Mt(e.nextSibling)}function Bn(){Ae=Re=null,$=!1}function ca(e){Qe===null?Qe=[e]:Qe.push(e)}var My=gt.ReactCurrentBatchConfig;function sr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var l=i.refs;o===null?delete l[s]:l[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function xi(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nc(e){var t=e._init;return t(e._payload)}function ih(e){function t(h,p){if(e){var m=h.deletions;m===null?(h.deletions=[p],h.flags|=16):m.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function i(h,p){return h=Nt(h,p),h.index=0,h.sibling=null,h}function s(h,p,m){return h.index=m,e?(m=h.alternate,m!==null?(m=m.index,m<p?(h.flags|=2,p):m):(h.flags|=2,p)):(h.flags|=1048576,p)}function o(h){return e&&h.alternate===null&&(h.flags|=2),h}function l(h,p,m,w){return p===null||p.tag!==6?(p=go(m,h.mode,w),p.return=h,p):(p=i(p,m),p.return=h,p)}function a(h,p,m,w){var x=m.type;return x===mn?c(h,p,m.props.children,w,m.key):p!==null&&(p.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===St&&nc(x)===p.type)?(w=i(p,m.props),w.ref=sr(h,p,m),w.return=h,w):(w=Ui(m.type,m.key,m.props,null,h.mode,w),w.ref=sr(h,p,m),w.return=h,w)}function u(h,p,m,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==m.containerInfo||p.stateNode.implementation!==m.implementation?(p=yo(m,h.mode,w),p.return=h,p):(p=i(p,m.children||[]),p.return=h,p)}function c(h,p,m,w,x){return p===null||p.tag!==7?(p=tn(m,h.mode,w,x),p.return=h,p):(p=i(p,m),p.return=h,p)}function f(h,p,m){if(typeof p=="string"&&p!==""||typeof p=="number")return p=go(""+p,h.mode,m),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ci:return m=Ui(p.type,p.key,p.props,null,h.mode,m),m.ref=sr(h,null,p),m.return=h,m;case pn:return p=yo(p,h.mode,m),p.return=h,p;case St:var w=p._init;return f(h,w(p._payload),m)}if(cr(p)||er(p))return p=tn(p,h.mode,m,null),p.return=h,p;xi(h,p)}return null}function d(h,p,m,w){var x=p!==null?p.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return x!==null?null:l(h,p,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ci:return m.key===x?a(h,p,m,w):null;case pn:return m.key===x?u(h,p,m,w):null;case St:return x=m._init,d(h,p,x(m._payload),w)}if(cr(m)||er(m))return x!==null?null:c(h,p,m,w,null);xi(h,m)}return null}function g(h,p,m,w,x){if(typeof w=="string"&&w!==""||typeof w=="number")return h=h.get(m)||null,l(p,h,""+w,x);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ci:return h=h.get(w.key===null?m:w.key)||null,a(p,h,w,x);case pn:return h=h.get(w.key===null?m:w.key)||null,u(p,h,w,x);case St:var C=w._init;return g(h,p,m,C(w._payload),x)}if(cr(w)||er(w))return h=h.get(m)||null,c(p,h,w,x,null);xi(p,w)}return null}function v(h,p,m,w){for(var x=null,C=null,A=p,P=p=0,j=null;A!==null&&P<m.length;P++){A.index>P?(j=A,A=null):j=A.sibling;var L=d(h,A,m[P],w);if(L===null){A===null&&(A=j);break}e&&A&&L.alternate===null&&t(h,A),p=s(L,p,P),C===null?x=L:C.sibling=L,C=L,A=j}if(P===m.length)return n(h,A),$&&Gt(h,P),x;if(A===null){for(;P<m.length;P++)A=f(h,m[P],w),A!==null&&(p=s(A,p,P),C===null?x=A:C.sibling=A,C=A);return $&&Gt(h,P),x}for(A=r(h,A);P<m.length;P++)j=g(A,h,P,m[P],w),j!==null&&(e&&j.alternate!==null&&A.delete(j.key===null?P:j.key),p=s(j,p,P),C===null?x=j:C.sibling=j,C=j);return e&&A.forEach(function(ne){return t(h,ne)}),$&&Gt(h,P),x}function y(h,p,m,w){var x=er(m);if(typeof x!="function")throw Error(T(150));if(m=x.call(m),m==null)throw Error(T(151));for(var C=x=null,A=p,P=p=0,j=null,L=m.next();A!==null&&!L.done;P++,L=m.next()){A.index>P?(j=A,A=null):j=A.sibling;var ne=d(h,A,L.value,w);if(ne===null){A===null&&(A=j);break}e&&A&&ne.alternate===null&&t(h,A),p=s(ne,p,P),C===null?x=ne:C.sibling=ne,C=ne,A=j}if(L.done)return n(h,A),$&&Gt(h,P),x;if(A===null){for(;!L.done;P++,L=m.next())L=f(h,L.value,w),L!==null&&(p=s(L,p,P),C===null?x=L:C.sibling=L,C=L);return $&&Gt(h,P),x}for(A=r(h,A);!L.done;P++,L=m.next())L=g(A,h,P,L.value,w),L!==null&&(e&&L.alternate!==null&&A.delete(L.key===null?P:L.key),p=s(L,p,P),C===null?x=L:C.sibling=L,C=L);return e&&A.forEach(function(yt){return t(h,yt)}),$&&Gt(h,P),x}function S(h,p,m,w){if(typeof m=="object"&&m!==null&&m.type===mn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case ci:e:{for(var x=m.key,C=p;C!==null;){if(C.key===x){if(x=m.type,x===mn){if(C.tag===7){n(h,C.sibling),p=i(C,m.props.children),p.return=h,h=p;break e}}else if(C.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===St&&nc(x)===C.type){n(h,C.sibling),p=i(C,m.props),p.ref=sr(h,C,m),p.return=h,h=p;break e}n(h,C);break}else t(h,C);C=C.sibling}m.type===mn?(p=tn(m.props.children,h.mode,w,m.key),p.return=h,h=p):(w=Ui(m.type,m.key,m.props,null,h.mode,w),w.ref=sr(h,p,m),w.return=h,h=w)}return o(h);case pn:e:{for(C=m.key;p!==null;){if(p.key===C)if(p.tag===4&&p.stateNode.containerInfo===m.containerInfo&&p.stateNode.implementation===m.implementation){n(h,p.sibling),p=i(p,m.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=yo(m,h.mode,w),p.return=h,h=p}return o(h);case St:return C=m._init,S(h,p,C(m._payload),w)}if(cr(m))return v(h,p,m,w);if(er(m))return y(h,p,m,w);xi(h,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,p!==null&&p.tag===6?(n(h,p.sibling),p=i(p,m),p.return=h,h=p):(n(h,p),p=go(m,h.mode,w),p.return=h,h=p),o(h)):n(h,p)}return S}var Un=ih(!0),sh=ih(!1),is=Ot(null),ss=null,Pn=null,fa=null;function da(){fa=Pn=ss=null}function ha(e){var t=is.current;B(is),e._currentValue=t}function al(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function In(e,t){ss=e,fa=Pn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function Be(e){var t=e._currentValue;if(fa!==e)if(e={context:e,memoizedValue:t,next:null},Pn===null){if(ss===null)throw Error(T(308));Pn=e,ss.dependencies={lanes:0,firstContext:e}}else Pn=Pn.next=e;return t}var qt=null;function pa(e){qt===null?qt=[e]:qt.push(e)}function oh(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,pa(t)):(n.next=i.next,i.next=n),t.interleaved=n,ht(e,r)}function ht(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xt=!1;function ma(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function lh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function at(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Dt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,I&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,ht(e,n)}return i=r.interleaved,i===null?(t.next=t,pa(r)):(t.next=i.next,i.next=t),r.interleaved=t,ht(e,n)}function _i(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ea(e,n)}}function rc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function os(e,t,n,r){var i=e.updateQueue;xt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?s=u:o.next=u,o=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==o&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(s!==null){var f=i.baseState;o=0,c=u=a=null,l=s;do{var d=l.lane,g=l.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var v=e,y=l;switch(d=t,g=n,y.tag){case 1:if(v=y.payload,typeof v=="function"){f=v.call(g,f,d);break e}f=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=y.payload,d=typeof v=="function"?v.call(g,f,d):v,d==null)break e;f=G({},f,d);break e;case 2:xt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[l]:d.push(l))}else g={eventTime:g,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=g,a=f):c=c.next=g,o|=d;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;d=l,l=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);ln|=o,e.lanes=o,e.memoizedState=f}}function ic(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(T(191,i));i.call(r)}}}var ti={},tt=Ot(ti),Br=Ot(ti),Ur=Ot(ti);function Jt(e){if(e===ti)throw Error(T(174));return e}function ga(e,t){switch(F(Ur,t),F(Br,e),F(tt,ti),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:$o(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=$o(t,e)}B(tt),F(tt,t)}function $n(){B(tt),B(Br),B(Ur)}function ah(e){Jt(Ur.current);var t=Jt(tt.current),n=$o(t,e.type);t!==n&&(F(Br,e),F(tt,n))}function ya(e){Br.current===e&&(B(tt),B(Br))}var W=Ot(0);function ls(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var uo=[];function va(){for(var e=0;e<uo.length;e++)uo[e]._workInProgressVersionPrimary=null;uo.length=0}var Ii=gt.ReactCurrentDispatcher,co=gt.ReactCurrentBatchConfig,on=0,H=null,ee=null,ie=null,as=!1,xr=!1,$r=0,Dy=0;function fe(){throw Error(T(321))}function wa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function Sa(e,t,n,r,i,s){if(on=s,H=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ii.current=e===null||e.memoizedState===null?jy:_y,e=n(r,i),xr){s=0;do{if(xr=!1,$r=0,25<=s)throw Error(T(301));s+=1,ie=ee=null,t.updateQueue=null,Ii.current=Iy,e=n(r,i)}while(xr)}if(Ii.current=us,t=ee!==null&&ee.next!==null,on=0,ie=ee=H=null,as=!1,t)throw Error(T(300));return e}function xa(){var e=$r!==0;return $r=0,e}function Je(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ie===null?H.memoizedState=ie=e:ie=ie.next=e,ie}function Ue(){if(ee===null){var e=H.alternate;e=e!==null?e.memoizedState:null}else e=ee.next;var t=ie===null?H.memoizedState:ie.next;if(t!==null)ie=t,ee=e;else{if(e===null)throw Error(T(310));ee=e,e={memoizedState:ee.memoizedState,baseState:ee.baseState,baseQueue:ee.baseQueue,queue:ee.queue,next:null},ie===null?H.memoizedState=ie=e:ie=ie.next=e}return ie}function Wr(e,t){return typeof t=="function"?t(e):t}function fo(e){var t=Ue(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=ee,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var l=o=null,a=null,u=s;do{var c=u.lane;if((on&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,o=r):a=a.next=f,H.lanes|=c,ln|=c}u=u.next}while(u!==null&&u!==s);a===null?o=r:a.next=l,Ze(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,H.lanes|=s,ln|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ho(e){var t=Ue(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);Ze(s,t.memoizedState)||(ke=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function uh(){}function ch(e,t){var n=H,r=Ue(),i=t(),s=!Ze(r.memoizedState,i);if(s&&(r.memoizedState=i,ke=!0),r=r.queue,ka(hh.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||ie!==null&&ie.memoizedState.tag&1){if(n.flags|=2048,Kr(9,dh.bind(null,n,r,i,t),void 0,null),se===null)throw Error(T(349));on&30||fh(n,t,i)}return i}function fh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=H.updateQueue,t===null?(t={lastEffect:null,stores:null},H.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function dh(e,t,n,r){t.value=n,t.getSnapshot=r,ph(t)&&mh(e)}function hh(e,t,n){return n(function(){ph(t)&&mh(e)})}function ph(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function mh(e){var t=ht(e,1);t!==null&&Ye(t,e,1,-1)}function sc(e){var t=Je();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Wr,lastRenderedState:e},t.queue=e,e=e.dispatch=Ny.bind(null,H,e),[t.memoizedState,e]}function Kr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=H.updateQueue,t===null?(t={lastEffect:null,stores:null},H.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function gh(){return Ue().memoizedState}function zi(e,t,n,r){var i=Je();H.flags|=e,i.memoizedState=Kr(1|t,n,void 0,r===void 0?null:r)}function Ms(e,t,n,r){var i=Ue();r=r===void 0?null:r;var s=void 0;if(ee!==null){var o=ee.memoizedState;if(s=o.destroy,r!==null&&wa(r,o.deps)){i.memoizedState=Kr(t,n,s,r);return}}H.flags|=e,i.memoizedState=Kr(1|t,n,s,r)}function oc(e,t){return zi(8390656,8,e,t)}function ka(e,t){return Ms(2048,8,e,t)}function yh(e,t){return Ms(4,2,e,t)}function vh(e,t){return Ms(4,4,e,t)}function wh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Sh(e,t,n){return n=n!=null?n.concat([e]):null,Ms(4,4,wh.bind(null,t,e),n)}function Pa(){}function xh(e,t){var n=Ue();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&wa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function kh(e,t){var n=Ue();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&wa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ph(e,t,n){return on&21?(Ze(n,t)||(n=Rd(),H.lanes|=n,ln|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function Ly(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=co.transition;co.transition={};try{e(!1),t()}finally{z=n,co.transition=r}}function Th(){return Ue().memoizedState}function Vy(e,t,n){var r=Vt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ch(e))Eh(t,n);else if(n=oh(e,t,n,r),n!==null){var i=ve();Ye(n,e,r,i),Ah(n,t,r)}}function Ny(e,t,n){var r=Vt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ch(e))Eh(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,l=s(o,n);if(i.hasEagerState=!0,i.eagerState=l,Ze(l,o)){var a=t.interleaved;a===null?(i.next=i,pa(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=oh(e,t,i,r),n!==null&&(i=ve(),Ye(n,e,r,i),Ah(n,t,r))}}function Ch(e){var t=e.alternate;return e===H||t!==null&&t===H}function Eh(e,t){xr=as=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ah(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ea(e,n)}}var us={readContext:Be,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},jy={readContext:Be,useCallback:function(e,t){return Je().memoizedState=[e,t===void 0?null:t],e},useContext:Be,useEffect:oc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,zi(4194308,4,wh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return zi(4194308,4,e,t)},useInsertionEffect:function(e,t){return zi(4,2,e,t)},useMemo:function(e,t){var n=Je();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Je();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Vy.bind(null,H,e),[r.memoizedState,e]},useRef:function(e){var t=Je();return e={current:e},t.memoizedState=e},useState:sc,useDebugValue:Pa,useDeferredValue:function(e){return Je().memoizedState=e},useTransition:function(){var e=sc(!1),t=e[0];return e=Ly.bind(null,e[1]),Je().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=H,i=Je();if($){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),se===null)throw Error(T(349));on&30||fh(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,oc(hh.bind(null,r,s,e),[e]),r.flags|=2048,Kr(9,dh.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Je(),t=se.identifierPrefix;if($){var n=lt,r=ot;n=(r&~(1<<32-Xe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=$r++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Dy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},_y={readContext:Be,useCallback:xh,useContext:Be,useEffect:ka,useImperativeHandle:Sh,useInsertionEffect:yh,useLayoutEffect:vh,useMemo:kh,useReducer:fo,useRef:gh,useState:function(){return fo(Wr)},useDebugValue:Pa,useDeferredValue:function(e){var t=Ue();return Ph(t,ee.memoizedState,e)},useTransition:function(){var e=fo(Wr)[0],t=Ue().memoizedState;return[e,t]},useMutableSource:uh,useSyncExternalStore:ch,useId:Th,unstable_isNewReconciler:!1},Iy={readContext:Be,useCallback:xh,useContext:Be,useEffect:ka,useImperativeHandle:Sh,useInsertionEffect:yh,useLayoutEffect:vh,useMemo:kh,useReducer:ho,useRef:gh,useState:function(){return ho(Wr)},useDebugValue:Pa,useDeferredValue:function(e){var t=Ue();return ee===null?t.memoizedState=e:Ph(t,ee.memoizedState,e)},useTransition:function(){var e=ho(Wr)[0],t=Ue().memoizedState;return[e,t]},useMutableSource:uh,useSyncExternalStore:ch,useId:Th,unstable_isNewReconciler:!1};function He(e,t){if(e&&e.defaultProps){t=G({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ul(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:G({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ds={isMounted:function(e){return(e=e._reactInternals)?cn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ve(),i=Vt(e),s=at(r,i);s.payload=t,n!=null&&(s.callback=n),t=Dt(e,s,i),t!==null&&(Ye(t,e,i,r),_i(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ve(),i=Vt(e),s=at(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Dt(e,s,i),t!==null&&(Ye(t,e,i,r),_i(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ve(),r=Vt(e),i=at(n,r);i.tag=2,t!=null&&(i.callback=t),t=Dt(e,i,r),t!==null&&(Ye(t,e,r,n),_i(t,e,r))}};function lc(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Ir(n,r)||!Ir(i,s):!0}function Rh(e,t,n){var r=!1,i=_t,s=t.contextType;return typeof s=="object"&&s!==null?s=Be(s):(i=Te(t)?rn:ge.current,r=t.contextTypes,s=(r=r!=null)?On(e,i):_t),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ds,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function ac(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ds.enqueueReplaceState(t,t.state,null)}function cl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},ma(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Be(s):(s=Te(t)?rn:ge.current,i.context=On(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(ul(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Ds.enqueueReplaceState(i,i.state,null),os(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Wn(e,t){try{var n="",r=t;do n+=cg(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function po(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function fl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var zy=typeof WeakMap=="function"?WeakMap:Map;function Mh(e,t,n){n=at(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){fs||(fs=!0,xl=r),fl(e,t)},n}function Dh(e,t,n){n=at(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){fl(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){fl(e,t),typeof r!="function"&&(Lt===null?Lt=new Set([this]):Lt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function uc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new zy;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=qy.bind(null,e,t,n),t.then(e,e))}function cc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function fc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=at(-1,1),t.tag=2,Dt(n,t,1))),n.lanes|=1),e)}var Fy=gt.ReactCurrentOwner,ke=!1;function ye(e,t,n,r){t.child=e===null?sh(t,null,n,r):Un(t,e.child,n,r)}function dc(e,t,n,r,i){n=n.render;var s=t.ref;return In(t,i),r=Sa(e,t,n,r,s,i),n=xa(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,pt(e,t,i)):($&&n&&aa(t),t.flags|=1,ye(e,t,r,i),t.child)}function hc(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!La(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Lh(e,t,s,r,i)):(e=Ui(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Ir,n(o,r)&&e.ref===t.ref)return pt(e,t,i)}return t.flags|=1,e=Nt(s,r),e.ref=t.ref,e.return=t,t.child=e}function Lh(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Ir(s,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,pt(e,t,i)}return dl(e,t,n,r,i)}function Vh(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(Cn,Ee),Ee|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(Cn,Ee),Ee|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,F(Cn,Ee),Ee|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,F(Cn,Ee),Ee|=r;return ye(e,t,i,n),t.child}function Nh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function dl(e,t,n,r,i){var s=Te(n)?rn:ge.current;return s=On(t,s),In(t,i),n=Sa(e,t,n,r,s,i),r=xa(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,pt(e,t,i)):($&&r&&aa(t),t.flags|=1,ye(e,t,n,i),t.child)}function pc(e,t,n,r,i){if(Te(n)){var s=!0;ts(t)}else s=!1;if(In(t,i),t.stateNode===null)Fi(e,t),Rh(t,n,r),cl(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Be(u):(u=Te(n)?rn:ge.current,u=On(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||a!==u)&&ac(t,o,r,u),xt=!1;var d=t.memoizedState;o.state=d,os(t,r,o,i),a=t.memoizedState,l!==r||d!==a||Pe.current||xt?(typeof c=="function"&&(ul(t,n,c,r),a=t.memoizedState),(l=xt||lc(t,n,l,r,d,a,u))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,lh(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:He(t.type,l),o.props=u,f=t.pendingProps,d=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=Be(a):(a=Te(n)?rn:ge.current,a=On(t,a));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==f||d!==a)&&ac(t,o,r,a),xt=!1,d=t.memoizedState,o.state=d,os(t,r,o,i);var v=t.memoizedState;l!==f||d!==v||Pe.current||xt?(typeof g=="function"&&(ul(t,n,g,r),v=t.memoizedState),(u=xt||lc(t,n,u,r,d,v,a)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=a,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return hl(e,t,n,r,s,i)}function hl(e,t,n,r,i,s){Nh(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&bu(t,n,!1),pt(e,t,s);r=t.stateNode,Fy.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Un(t,e.child,null,s),t.child=Un(t,null,l,s)):ye(e,t,l,s),t.memoizedState=r.state,i&&bu(t,n,!0),t.child}function jh(e){var t=e.stateNode;t.pendingContext?Ju(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ju(e,t.context,!1),ga(e,t.containerInfo)}function mc(e,t,n,r,i){return Bn(),ca(i),t.flags|=256,ye(e,t,n,r),t.child}var pl={dehydrated:null,treeContext:null,retryLane:0};function ml(e){return{baseLanes:e,cachePool:null,transitions:null}}function _h(e,t,n){var r=t.pendingProps,i=W.current,s=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),F(W,i&1),e===null)return ll(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=Ns(o,r,0,null),e=tn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=ml(n),t.memoizedState=pl,e):Ta(t,o));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Oy(e,t,o,r,l,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Nt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?s=Nt(l,s):(s=tn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?ml(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=pl,r}return s=e.child,e=s.sibling,r=Nt(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ta(e,t){return t=Ns({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ki(e,t,n,r){return r!==null&&ca(r),Un(t,e.child,null,n),e=Ta(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Oy(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=po(Error(T(422))),ki(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Ns({mode:"visible",children:r.children},i,0,null),s=tn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Un(t,e.child,null,o),t.child.memoizedState=ml(o),t.memoizedState=pl,s);if(!(t.mode&1))return ki(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(T(419)),r=po(s,r,void 0),ki(e,t,o,r)}if(l=(o&e.childLanes)!==0,ke||l){if(r=se,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,ht(e,i),Ye(r,e,i,-1))}return Da(),r=po(Error(T(421))),ki(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Jy.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Ae=Mt(i.nextSibling),Re=t,$=!0,Qe=null,e!==null&&(Ie[ze++]=ot,Ie[ze++]=lt,Ie[ze++]=sn,ot=e.id,lt=e.overflow,sn=t),t=Ta(t,r.children),t.flags|=4096,t)}function gc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),al(e.return,t,n)}function mo(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function Ih(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(ye(e,t,r.children,n),r=W.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&gc(e,n,t);else if(e.tag===19)gc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F(W,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&ls(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),mo(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&ls(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}mo(t,!0,n,null,s);break;case"together":mo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Fi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function pt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ln|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Nt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Nt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function By(e,t,n){switch(t.tag){case 3:jh(t),Bn();break;case 5:ah(t);break;case 1:Te(t.type)&&ts(t);break;case 4:ga(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;F(is,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F(W,W.current&1),t.flags|=128,null):n&t.child.childLanes?_h(e,t,n):(F(W,W.current&1),e=pt(e,t,n),e!==null?e.sibling:null);F(W,W.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ih(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),F(W,W.current),r)break;return null;case 22:case 23:return t.lanes=0,Vh(e,t,n)}return pt(e,t,n)}var zh,gl,Fh,Oh;zh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};gl=function(){};Fh=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Jt(tt.current);var s=null;switch(n){case"input":i=Fo(e,i),r=Fo(e,r),s=[];break;case"select":i=G({},i,{value:void 0}),r=G({},r,{value:void 0}),s=[];break;case"textarea":i=Uo(e,i),r=Uo(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=bi)}Wo(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Mr.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Mr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&O("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Oh=function(e,t,n,r){n!==r&&(t.flags|=4)};function or(e,t){if(!$)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Uy(e,t,n){var r=t.pendingProps;switch(ua(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Te(t.type)&&es(),de(t),null;case 3:return r=t.stateNode,$n(),B(Pe),B(ge),va(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Si(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Qe!==null&&(Tl(Qe),Qe=null))),gl(e,t),de(t),null;case 5:ya(t);var i=Jt(Ur.current);if(n=t.type,e!==null&&t.stateNode!=null)Fh(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return de(t),null}if(e=Jt(tt.current),Si(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[be]=t,r[Or]=s,e=(t.mode&1)!==0,n){case"dialog":O("cancel",r),O("close",r);break;case"iframe":case"object":case"embed":O("load",r);break;case"video":case"audio":for(i=0;i<dr.length;i++)O(dr[i],r);break;case"source":O("error",r);break;case"img":case"image":case"link":O("error",r),O("load",r);break;case"details":O("toggle",r);break;case"input":Cu(r,s),O("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},O("invalid",r);break;case"textarea":Au(r,s),O("invalid",r)}Wo(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&wi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&wi(r.textContent,l,e),i=["children",""+l]):Mr.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&O("scroll",r)}switch(n){case"input":fi(r),Eu(r,s,!0);break;case"textarea":fi(r),Ru(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=bi)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=hd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[be]=t,e[Or]=r,zh(e,t,!1,!1),t.stateNode=e;e:{switch(o=Ko(n,r),n){case"dialog":O("cancel",e),O("close",e),i=r;break;case"iframe":case"object":case"embed":O("load",e),i=r;break;case"video":case"audio":for(i=0;i<dr.length;i++)O(dr[i],e);i=r;break;case"source":O("error",e),i=r;break;case"img":case"image":case"link":O("error",e),O("load",e),i=r;break;case"details":O("toggle",e),i=r;break;case"input":Cu(e,r),i=Fo(e,r),O("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=G({},r,{value:void 0}),O("invalid",e);break;case"textarea":Au(e,r),i=Uo(e,r),O("invalid",e);break;default:i=r}Wo(n,i),l=i;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?gd(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&pd(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Dr(e,a):typeof a=="number"&&Dr(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Mr.hasOwnProperty(s)?a!=null&&s==="onScroll"&&O("scroll",e):a!=null&&Xl(e,s,a,o))}switch(n){case"input":fi(e),Eu(e,r,!1);break;case"textarea":fi(e),Ru(e);break;case"option":r.value!=null&&e.setAttribute("value",""+jt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Vn(e,!!r.multiple,s,!1):r.defaultValue!=null&&Vn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=bi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)Oh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=Jt(Ur.current),Jt(tt.current),Si(t)){if(r=t.stateNode,n=t.memoizedProps,r[be]=t,(s=r.nodeValue!==n)&&(e=Re,e!==null))switch(e.tag){case 3:wi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&wi(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[be]=t,t.stateNode=r}return de(t),null;case 13:if(B(W),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if($&&Ae!==null&&t.mode&1&&!(t.flags&128))rh(),Bn(),t.flags|=98560,s=!1;else if(s=Si(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(T(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(T(317));s[be]=t}else Bn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),s=!1}else Qe!==null&&(Tl(Qe),Qe=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||W.current&1?te===0&&(te=3):Da())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return $n(),gl(e,t),e===null&&zr(t.stateNode.containerInfo),de(t),null;case 10:return ha(t.type._context),de(t),null;case 17:return Te(t.type)&&es(),de(t),null;case 19:if(B(W),s=t.memoizedState,s===null)return de(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)or(s,!1);else{if(te!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=ls(e),o!==null){for(t.flags|=128,or(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F(W,W.current&1|2),t.child}e=e.sibling}s.tail!==null&&q()>Kn&&(t.flags|=128,r=!0,or(s,!1),t.lanes=4194304)}else{if(!r)if(e=ls(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),or(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!$)return de(t),null}else 2*q()-s.renderingStartTime>Kn&&n!==1073741824&&(t.flags|=128,r=!0,or(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=q(),t.sibling=null,n=W.current,F(W,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return Ma(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ee&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function $y(e,t){switch(ua(t),t.tag){case 1:return Te(t.type)&&es(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return $n(),B(Pe),B(ge),va(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ya(t),null;case 13:if(B(W),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));Bn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return B(W),null;case 4:return $n(),null;case 10:return ha(t.type._context),null;case 22:case 23:return Ma(),null;case 24:return null;default:return null}}var Pi=!1,pe=!1,Wy=typeof WeakSet=="function"?WeakSet:Set,M=null;function Tn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){X(e,t,r)}else n.current=null}function yl(e,t,n){try{n()}catch(r){X(e,t,r)}}var yc=!1;function Ky(e,t){if(el=Zi,e=Kd(),la(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var g;f!==n||i!==0&&f.nodeType!==3||(l=o+i),f!==s||r!==0&&f.nodeType!==3||(a=o+r),f.nodeType===3&&(o+=f.nodeValue.length),(g=f.firstChild)!==null;)d=f,f=g;for(;;){if(f===e)break t;if(d===n&&++u===i&&(l=o),d===s&&++c===r&&(a=o),(g=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(tl={focusedElem:e,selectionRange:n},Zi=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var y=v.memoizedProps,S=v.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?y:He(t.type,y),S);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(w){X(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return v=yc,yc=!1,v}function kr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&yl(t,n,s)}i=i.next}while(i!==r)}}function Ls(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function vl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Bh(e){var t=e.alternate;t!==null&&(e.alternate=null,Bh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[be],delete t[Or],delete t[il],delete t[Ey],delete t[Ay])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Uh(e){return e.tag===5||e.tag===3||e.tag===4}function vc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Uh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function wl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=bi));else if(r!==4&&(e=e.child,e!==null))for(wl(e,t,n),e=e.sibling;e!==null;)wl(e,t,n),e=e.sibling}function Sl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Sl(e,t,n),e=e.sibling;e!==null;)Sl(e,t,n),e=e.sibling}var oe=null,Ge=!1;function vt(e,t,n){for(n=n.child;n!==null;)$h(e,t,n),n=n.sibling}function $h(e,t,n){if(et&&typeof et.onCommitFiberUnmount=="function")try{et.onCommitFiberUnmount(Ps,n)}catch{}switch(n.tag){case 5:pe||Tn(n,t);case 6:var r=oe,i=Ge;oe=null,vt(e,t,n),oe=r,Ge=i,oe!==null&&(Ge?(e=oe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):oe.removeChild(n.stateNode));break;case 18:oe!==null&&(Ge?(e=oe,n=n.stateNode,e.nodeType===8?lo(e.parentNode,n):e.nodeType===1&&lo(e,n),jr(e)):lo(oe,n.stateNode));break;case 4:r=oe,i=Ge,oe=n.stateNode.containerInfo,Ge=!0,vt(e,t,n),oe=r,Ge=i;break;case 0:case 11:case 14:case 15:if(!pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&yl(n,t,o),i=i.next}while(i!==r)}vt(e,t,n);break;case 1:if(!pe&&(Tn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){X(n,t,l)}vt(e,t,n);break;case 21:vt(e,t,n);break;case 22:n.mode&1?(pe=(r=pe)||n.memoizedState!==null,vt(e,t,n),pe=r):vt(e,t,n);break;default:vt(e,t,n)}}function wc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Wy),t.forEach(function(r){var i=by.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function We(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:oe=l.stateNode,Ge=!1;break e;case 3:oe=l.stateNode.containerInfo,Ge=!0;break e;case 4:oe=l.stateNode.containerInfo,Ge=!0;break e}l=l.return}if(oe===null)throw Error(T(160));$h(s,o,i),oe=null,Ge=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){X(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Wh(t,e),t=t.sibling}function Wh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(We(t,e),qe(e),r&4){try{kr(3,e,e.return),Ls(3,e)}catch(y){X(e,e.return,y)}try{kr(5,e,e.return)}catch(y){X(e,e.return,y)}}break;case 1:We(t,e),qe(e),r&512&&n!==null&&Tn(n,n.return);break;case 5:if(We(t,e),qe(e),r&512&&n!==null&&Tn(n,n.return),e.flags&32){var i=e.stateNode;try{Dr(i,"")}catch(y){X(e,e.return,y)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&fd(i,s),Ko(l,o);var u=Ko(l,s);for(o=0;o<a.length;o+=2){var c=a[o],f=a[o+1];c==="style"?gd(i,f):c==="dangerouslySetInnerHTML"?pd(i,f):c==="children"?Dr(i,f):Xl(i,c,f,u)}switch(l){case"input":Oo(i,s);break;case"textarea":dd(i,s);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var g=s.value;g!=null?Vn(i,!!s.multiple,g,!1):d!==!!s.multiple&&(s.defaultValue!=null?Vn(i,!!s.multiple,s.defaultValue,!0):Vn(i,!!s.multiple,s.multiple?[]:"",!1))}i[Or]=s}catch(y){X(e,e.return,y)}}break;case 6:if(We(t,e),qe(e),r&4){if(e.stateNode===null)throw Error(T(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(y){X(e,e.return,y)}}break;case 3:if(We(t,e),qe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{jr(t.containerInfo)}catch(y){X(e,e.return,y)}break;case 4:We(t,e),qe(e);break;case 13:We(t,e),qe(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Aa=q())),r&4&&wc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(pe=(u=pe)||c,We(t,e),pe=u):We(t,e),qe(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(M=e,c=e.child;c!==null;){for(f=M=c;M!==null;){switch(d=M,g=d.child,d.tag){case 0:case 11:case 14:case 15:kr(4,d,d.return);break;case 1:Tn(d,d.return);var v=d.stateNode;if(typeof v.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(y){X(r,n,y)}}break;case 5:Tn(d,d.return);break;case 22:if(d.memoizedState!==null){xc(f);continue}}g!==null?(g.return=d,M=g):xc(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=f.stateNode,a=f.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=md("display",o))}catch(y){X(e,e.return,y)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(y){X(e,e.return,y)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:We(t,e),qe(e),r&4&&wc(e);break;case 21:break;default:We(t,e),qe(e)}}function qe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Uh(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Dr(i,""),r.flags&=-33);var s=vc(e);Sl(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,l=vc(e);wl(e,l,o);break;default:throw Error(T(161))}}catch(a){X(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hy(e,t,n){M=e,Kh(e)}function Kh(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var i=M,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Pi;if(!o){var l=i.alternate,a=l!==null&&l.memoizedState!==null||pe;l=Pi;var u=pe;if(Pi=o,(pe=a)&&!u)for(M=i;M!==null;)o=M,a=o.child,o.tag===22&&o.memoizedState!==null?kc(i):a!==null?(a.return=o,M=a):kc(i);for(;s!==null;)M=s,Kh(s),s=s.sibling;M=i,Pi=l,pe=u}Sc(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,M=s):Sc(e)}}function Sc(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:pe||Ls(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!pe)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:He(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&ic(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ic(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&jr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}pe||t.flags&512&&vl(t)}catch(d){X(t,t.return,d)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function xc(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function kc(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ls(4,t)}catch(a){X(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){X(t,i,a)}}var s=t.return;try{vl(t)}catch(a){X(t,s,a)}break;case 5:var o=t.return;try{vl(t)}catch(a){X(t,o,a)}}}catch(a){X(t,t.return,a)}if(t===e){M=null;break}var l=t.sibling;if(l!==null){l.return=t.return,M=l;break}M=t.return}}var Gy=Math.ceil,cs=gt.ReactCurrentDispatcher,Ca=gt.ReactCurrentOwner,Oe=gt.ReactCurrentBatchConfig,I=0,se=null,J=null,ue=0,Ee=0,Cn=Ot(0),te=0,Hr=null,ln=0,Vs=0,Ea=0,Pr=null,xe=null,Aa=0,Kn=1/0,it=null,fs=!1,xl=null,Lt=null,Ti=!1,Ct=null,ds=0,Tr=0,kl=null,Oi=-1,Bi=0;function ve(){return I&6?q():Oi!==-1?Oi:Oi=q()}function Vt(e){return e.mode&1?I&2&&ue!==0?ue&-ue:My.transition!==null?(Bi===0&&(Bi=Rd()),Bi):(e=z,e!==0||(e=window.event,e=e===void 0?16:_d(e.type)),e):1}function Ye(e,t,n,r){if(50<Tr)throw Tr=0,kl=null,Error(T(185));Jr(e,n,r),(!(I&2)||e!==se)&&(e===se&&(!(I&2)&&(Vs|=n),te===4&&Pt(e,ue)),Ce(e,r),n===1&&I===0&&!(t.mode&1)&&(Kn=q()+500,Rs&&Bt()))}function Ce(e,t){var n=e.callbackNode;Mg(e,t);var r=Yi(e,e===se?ue:0);if(r===0)n!==null&&Lu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Lu(n),t===1)e.tag===0?Ry(Pc.bind(null,e)):eh(Pc.bind(null,e)),Ty(function(){!(I&6)&&Bt()}),n=null;else{switch(Md(r)){case 1:n=bl;break;case 4:n=Ed;break;case 16:n=Xi;break;case 536870912:n=Ad;break;default:n=Xi}n=Jh(n,Hh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Hh(e,t){if(Oi=-1,Bi=0,I&6)throw Error(T(327));var n=e.callbackNode;if(zn()&&e.callbackNode!==n)return null;var r=Yi(e,e===se?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=hs(e,r);else{t=r;var i=I;I|=2;var s=Qh();(se!==e||ue!==t)&&(it=null,Kn=q()+500,en(e,t));do try{Yy();break}catch(l){Gh(e,l)}while(!0);da(),cs.current=s,I=i,J!==null?t=0:(se=null,ue=0,t=te)}if(t!==0){if(t===2&&(i=Yo(e),i!==0&&(r=i,t=Pl(e,i))),t===1)throw n=Hr,en(e,0),Pt(e,r),Ce(e,q()),n;if(t===6)Pt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Qy(i)&&(t=hs(e,r),t===2&&(s=Yo(e),s!==0&&(r=s,t=Pl(e,s))),t===1))throw n=Hr,en(e,0),Pt(e,r),Ce(e,q()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:Qt(e,xe,it);break;case 3:if(Pt(e,r),(r&130023424)===r&&(t=Aa+500-q(),10<t)){if(Yi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ve(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=rl(Qt.bind(null,e,xe,it),t);break}Qt(e,xe,it);break;case 4:if(Pt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-Xe(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=q()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Gy(r/1960))-r,10<r){e.timeoutHandle=rl(Qt.bind(null,e,xe,it),r);break}Qt(e,xe,it);break;case 5:Qt(e,xe,it);break;default:throw Error(T(329))}}}return Ce(e,q()),e.callbackNode===n?Hh.bind(null,e):null}function Pl(e,t){var n=Pr;return e.current.memoizedState.isDehydrated&&(en(e,t).flags|=256),e=hs(e,t),e!==2&&(t=xe,xe=n,t!==null&&Tl(t)),e}function Tl(e){xe===null?xe=e:xe.push.apply(xe,e)}function Qy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!Ze(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Pt(e,t){for(t&=~Ea,t&=~Vs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Xe(t),r=1<<n;e[n]=-1,t&=~r}}function Pc(e){if(I&6)throw Error(T(327));zn();var t=Yi(e,0);if(!(t&1))return Ce(e,q()),null;var n=hs(e,t);if(e.tag!==0&&n===2){var r=Yo(e);r!==0&&(t=r,n=Pl(e,r))}if(n===1)throw n=Hr,en(e,0),Pt(e,t),Ce(e,q()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Qt(e,xe,it),Ce(e,q()),null}function Ra(e,t){var n=I;I|=1;try{return e(t)}finally{I=n,I===0&&(Kn=q()+500,Rs&&Bt())}}function an(e){Ct!==null&&Ct.tag===0&&!(I&6)&&zn();var t=I;I|=1;var n=Oe.transition,r=z;try{if(Oe.transition=null,z=1,e)return e()}finally{z=r,Oe.transition=n,I=t,!(I&6)&&Bt()}}function Ma(){Ee=Cn.current,B(Cn)}function en(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Py(n)),J!==null)for(n=J.return;n!==null;){var r=n;switch(ua(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&es();break;case 3:$n(),B(Pe),B(ge),va();break;case 5:ya(r);break;case 4:$n();break;case 13:B(W);break;case 19:B(W);break;case 10:ha(r.type._context);break;case 22:case 23:Ma()}n=n.return}if(se=e,J=e=Nt(e.current,null),ue=Ee=t,te=0,Hr=null,Ea=Vs=ln=0,xe=Pr=null,qt!==null){for(t=0;t<qt.length;t++)if(n=qt[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}qt=null}return e}function Gh(e,t){do{var n=J;try{if(da(),Ii.current=us,as){for(var r=H.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}as=!1}if(on=0,ie=ee=H=null,xr=!1,$r=0,Ca.current=null,n===null||n.return===null){te=1,Hr=t,J=null;break}e:{var s=e,o=n.return,l=n,a=t;if(t=ue,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=cc(o);if(g!==null){g.flags&=-257,fc(g,o,l,s,t),g.mode&1&&uc(s,u,t),t=g,a=u;var v=t.updateQueue;if(v===null){var y=new Set;y.add(a),t.updateQueue=y}else v.add(a);break e}else{if(!(t&1)){uc(s,u,t),Da();break e}a=Error(T(426))}}else if($&&l.mode&1){var S=cc(o);if(S!==null){!(S.flags&65536)&&(S.flags|=256),fc(S,o,l,s,t),ca(Wn(a,l));break e}}s=a=Wn(a,l),te!==4&&(te=2),Pr===null?Pr=[s]:Pr.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var h=Mh(s,a,t);rc(s,h);break e;case 1:l=a;var p=s.type,m=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Lt===null||!Lt.has(m)))){s.flags|=65536,t&=-t,s.lanes|=t;var w=Dh(s,l,t);rc(s,w);break e}}s=s.return}while(s!==null)}Yh(n)}catch(x){t=x,J===n&&n!==null&&(J=n=n.return);continue}break}while(!0)}function Qh(){var e=cs.current;return cs.current=us,e===null?us:e}function Da(){(te===0||te===3||te===2)&&(te=4),se===null||!(ln&268435455)&&!(Vs&268435455)||Pt(se,ue)}function hs(e,t){var n=I;I|=2;var r=Qh();(se!==e||ue!==t)&&(it=null,en(e,t));do try{Xy();break}catch(i){Gh(e,i)}while(!0);if(da(),I=n,cs.current=r,J!==null)throw Error(T(261));return se=null,ue=0,te}function Xy(){for(;J!==null;)Xh(J)}function Yy(){for(;J!==null&&!Sg();)Xh(J)}function Xh(e){var t=qh(e.alternate,e,Ee);e.memoizedProps=e.pendingProps,t===null?Yh(e):J=t,Ca.current=null}function Yh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=$y(n,t),n!==null){n.flags&=32767,J=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{te=6,J=null;return}}else if(n=Uy(n,t,Ee),n!==null){J=n;return}if(t=t.sibling,t!==null){J=t;return}J=t=e}while(t!==null);te===0&&(te=5)}function Qt(e,t,n){var r=z,i=Oe.transition;try{Oe.transition=null,z=1,Zy(e,t,n,r)}finally{Oe.transition=i,z=r}return null}function Zy(e,t,n,r){do zn();while(Ct!==null);if(I&6)throw Error(T(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Dg(e,s),e===se&&(J=se=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ti||(Ti=!0,Jh(Xi,function(){return zn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Oe.transition,Oe.transition=null;var o=z;z=1;var l=I;I|=4,Ca.current=null,Ky(e,n),Wh(n,e),gy(tl),Zi=!!el,tl=el=null,e.current=n,Hy(n),xg(),I=l,z=o,Oe.transition=s}else e.current=n;if(Ti&&(Ti=!1,Ct=e,ds=i),s=e.pendingLanes,s===0&&(Lt=null),Tg(n.stateNode),Ce(e,q()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(fs)throw fs=!1,e=xl,xl=null,e;return ds&1&&e.tag!==0&&zn(),s=e.pendingLanes,s&1?e===kl?Tr++:(Tr=0,kl=e):Tr=0,Bt(),null}function zn(){if(Ct!==null){var e=Md(ds),t=Oe.transition,n=z;try{if(Oe.transition=null,z=16>e?16:e,Ct===null)var r=!1;else{if(e=Ct,Ct=null,ds=0,I&6)throw Error(T(331));var i=I;for(I|=4,M=e.current;M!==null;){var s=M,o=s.child;if(M.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(M=u;M!==null;){var c=M;switch(c.tag){case 0:case 11:case 15:kr(8,c,s)}var f=c.child;if(f!==null)f.return=c,M=f;else for(;M!==null;){c=M;var d=c.sibling,g=c.return;if(Bh(c),c===u){M=null;break}if(d!==null){d.return=g,M=d;break}M=g}}}var v=s.alternate;if(v!==null){var y=v.child;if(y!==null){v.child=null;do{var S=y.sibling;y.sibling=null,y=S}while(y!==null)}}M=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,M=o;else e:for(;M!==null;){if(s=M,s.flags&2048)switch(s.tag){case 0:case 11:case 15:kr(9,s,s.return)}var h=s.sibling;if(h!==null){h.return=s.return,M=h;break e}M=s.return}}var p=e.current;for(M=p;M!==null;){o=M;var m=o.child;if(o.subtreeFlags&2064&&m!==null)m.return=o,M=m;else e:for(o=p;M!==null;){if(l=M,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Ls(9,l)}}catch(x){X(l,l.return,x)}if(l===o){M=null;break e}var w=l.sibling;if(w!==null){w.return=l.return,M=w;break e}M=l.return}}if(I=i,Bt(),et&&typeof et.onPostCommitFiberRoot=="function")try{et.onPostCommitFiberRoot(Ps,e)}catch{}r=!0}return r}finally{z=n,Oe.transition=t}}return!1}function Tc(e,t,n){t=Wn(n,t),t=Mh(e,t,1),e=Dt(e,t,1),t=ve(),e!==null&&(Jr(e,1,t),Ce(e,t))}function X(e,t,n){if(e.tag===3)Tc(e,e,n);else for(;t!==null;){if(t.tag===3){Tc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Lt===null||!Lt.has(r))){e=Wn(n,e),e=Dh(t,e,1),t=Dt(t,e,1),e=ve(),t!==null&&(Jr(t,1,e),Ce(t,e));break}}t=t.return}}function qy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ve(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(ue&n)===n&&(te===4||te===3&&(ue&130023424)===ue&&500>q()-Aa?en(e,0):Ea|=n),Ce(e,t)}function Zh(e,t){t===0&&(e.mode&1?(t=pi,pi<<=1,!(pi&130023424)&&(pi=4194304)):t=1);var n=ve();e=ht(e,t),e!==null&&(Jr(e,t,n),Ce(e,n))}function Jy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Zh(e,n)}function by(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),Zh(e,n)}var qh;qh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Pe.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,By(e,t,n);ke=!!(e.flags&131072)}else ke=!1,$&&t.flags&1048576&&th(t,rs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Fi(e,t),e=t.pendingProps;var i=On(t,ge.current);In(t,n),i=Sa(null,t,r,e,i,n);var s=xa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Te(r)?(s=!0,ts(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,ma(t),i.updater=Ds,t.stateNode=i,i._reactInternals=t,cl(t,r,e,n),t=hl(null,t,r,!0,s,n)):(t.tag=0,$&&s&&aa(t),ye(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Fi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=tv(r),e=He(r,e),i){case 0:t=dl(null,t,r,e,n);break e;case 1:t=pc(null,t,r,e,n);break e;case 11:t=dc(null,t,r,e,n);break e;case 14:t=hc(null,t,r,He(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:He(r,i),dl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:He(r,i),pc(e,t,r,i,n);case 3:e:{if(jh(t),e===null)throw Error(T(387));r=t.pendingProps,s=t.memoizedState,i=s.element,lh(e,t),os(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Wn(Error(T(423)),t),t=mc(e,t,r,n,i);break e}else if(r!==i){i=Wn(Error(T(424)),t),t=mc(e,t,r,n,i);break e}else for(Ae=Mt(t.stateNode.containerInfo.firstChild),Re=t,$=!0,Qe=null,n=sh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Bn(),r===i){t=pt(e,t,n);break e}ye(e,t,r,n)}t=t.child}return t;case 5:return ah(t),e===null&&ll(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,nl(r,i)?o=null:s!==null&&nl(r,s)&&(t.flags|=32),Nh(e,t),ye(e,t,o,n),t.child;case 6:return e===null&&ll(t),null;case 13:return _h(e,t,n);case 4:return ga(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Un(t,null,r,n):ye(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:He(r,i),dc(e,t,r,i,n);case 7:return ye(e,t,t.pendingProps,n),t.child;case 8:return ye(e,t,t.pendingProps.children,n),t.child;case 12:return ye(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,F(is,r._currentValue),r._currentValue=o,s!==null)if(Ze(s.value,o)){if(s.children===i.children&&!Pe.current){t=pt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){o=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=at(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),al(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(T(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),al(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}ye(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,In(t,n),i=Be(i),r=r(i),t.flags|=1,ye(e,t,r,n),t.child;case 14:return r=t.type,i=He(r,t.pendingProps),i=He(r.type,i),hc(e,t,r,i,n);case 15:return Lh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:He(r,i),Fi(e,t),t.tag=1,Te(r)?(e=!0,ts(t)):e=!1,In(t,n),Rh(t,r,i),cl(t,r,i,n),hl(null,t,r,!0,e,n);case 19:return Ih(e,t,n);case 22:return Vh(e,t,n)}throw Error(T(156,t.tag))};function Jh(e,t){return Cd(e,t)}function ev(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Fe(e,t,n,r){return new ev(e,t,n,r)}function La(e){return e=e.prototype,!(!e||!e.isReactComponent)}function tv(e){if(typeof e=="function")return La(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Zl)return 11;if(e===ql)return 14}return 2}function Nt(e,t){var n=e.alternate;return n===null?(n=Fe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ui(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")La(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case mn:return tn(n.children,i,s,t);case Yl:o=8,i|=8;break;case jo:return e=Fe(12,n,t,i|2),e.elementType=jo,e.lanes=s,e;case _o:return e=Fe(13,n,t,i),e.elementType=_o,e.lanes=s,e;case Io:return e=Fe(19,n,t,i),e.elementType=Io,e.lanes=s,e;case ad:return Ns(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case od:o=10;break e;case ld:o=9;break e;case Zl:o=11;break e;case ql:o=14;break e;case St:o=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=Fe(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function tn(e,t,n,r){return e=Fe(7,e,r,t),e.lanes=n,e}function Ns(e,t,n,r){return e=Fe(22,e,r,t),e.elementType=ad,e.lanes=n,e.stateNode={isHidden:!1},e}function go(e,t,n){return e=Fe(6,e,null,t),e.lanes=n,e}function yo(e,t,n){return t=Fe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function nv(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Zs(0),this.expirationTimes=Zs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zs(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Va(e,t,n,r,i,s,o,l,a){return e=new nv(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Fe(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ma(s),e}function rv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:pn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function bh(e){if(!e)return _t;e=e._reactInternals;e:{if(cn(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Te(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Te(n))return bd(e,n,t)}return t}function ep(e,t,n,r,i,s,o,l,a){return e=Va(n,r,!0,e,i,s,o,l,a),e.context=bh(null),n=e.current,r=ve(),i=Vt(n),s=at(r,i),s.callback=t??null,Dt(n,s,i),e.current.lanes=i,Jr(e,i,r),Ce(e,r),e}function js(e,t,n,r){var i=t.current,s=ve(),o=Vt(i);return n=bh(n),t.context===null?t.context=n:t.pendingContext=n,t=at(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Dt(i,t,o),e!==null&&(Ye(e,i,o,s),_i(e,i,o)),o}function ps(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Cc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Na(e,t){Cc(e,t),(e=e.alternate)&&Cc(e,t)}function iv(){return null}var tp=typeof reportError=="function"?reportError:function(e){console.error(e)};function ja(e){this._internalRoot=e}_s.prototype.render=ja.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));js(e,t,null,null)};_s.prototype.unmount=ja.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;an(function(){js(null,e,null,null)}),t[dt]=null}};function _s(e){this._internalRoot=e}_s.prototype.unstable_scheduleHydration=function(e){if(e){var t=Vd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<kt.length&&t!==0&&t<kt[n].priority;n++);kt.splice(n,0,e),n===0&&jd(e)}};function _a(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Is(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ec(){}function sv(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=ps(o);s.call(u)}}var o=ep(t,r,e,0,null,!1,!1,"",Ec);return e._reactRootContainer=o,e[dt]=o.current,zr(e.nodeType===8?e.parentNode:e),an(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=ps(a);l.call(u)}}var a=Va(e,0,!1,null,null,!1,!1,"",Ec);return e._reactRootContainer=a,e[dt]=a.current,zr(e.nodeType===8?e.parentNode:e),an(function(){js(t,a,n,r)}),a}function zs(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var l=i;i=function(){var a=ps(o);l.call(a)}}js(t,o,e,i)}else o=sv(n,t,e,i,r);return ps(o)}Dd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fr(t.pendingLanes);n!==0&&(ea(t,n|1),Ce(t,q()),!(I&6)&&(Kn=q()+500,Bt()))}break;case 13:an(function(){var r=ht(e,1);if(r!==null){var i=ve();Ye(r,e,1,i)}}),Na(e,1)}};ta=function(e){if(e.tag===13){var t=ht(e,134217728);if(t!==null){var n=ve();Ye(t,e,134217728,n)}Na(e,134217728)}};Ld=function(e){if(e.tag===13){var t=Vt(e),n=ht(e,t);if(n!==null){var r=ve();Ye(n,e,t,r)}Na(e,t)}};Vd=function(){return z};Nd=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};Go=function(e,t,n){switch(t){case"input":if(Oo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=As(r);if(!i)throw Error(T(90));cd(r),Oo(r,i)}}}break;case"textarea":dd(e,n);break;case"select":t=n.value,t!=null&&Vn(e,!!n.multiple,t,!1)}};wd=Ra;Sd=an;var ov={usingClientEntryPoint:!1,Events:[ei,wn,As,yd,vd,Ra]},lr={findFiberByHostInstance:Zt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},lv={bundleType:lr.bundleType,version:lr.version,rendererPackageName:lr.rendererPackageName,rendererConfig:lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:gt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Pd(e),e===null?null:e.stateNode},findFiberByHostInstance:lr.findFiberByHostInstance||iv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ci=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ci.isDisabled&&Ci.supportsFiber)try{Ps=Ci.inject(lv),et=Ci}catch{}}Ve.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ov;Ve.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!_a(t))throw Error(T(200));return rv(e,t,null,n)};Ve.createRoot=function(e,t){if(!_a(e))throw Error(T(299));var n=!1,r="",i=tp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Va(e,1,!1,null,null,n,!1,r,i),e[dt]=t.current,zr(e.nodeType===8?e.parentNode:e),new ja(t)};Ve.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=Pd(t),e=e===null?null:e.stateNode,e};Ve.flushSync=function(e){return an(e)};Ve.hydrate=function(e,t,n){if(!Is(t))throw Error(T(200));return zs(null,e,t,!0,n)};Ve.hydrateRoot=function(e,t,n){if(!_a(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=tp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=ep(t,null,e,1,n??null,i,!1,s,o),e[dt]=t.current,zr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new _s(t)};Ve.render=function(e,t,n){if(!Is(t))throw Error(T(200));return zs(null,e,t,!1,n)};Ve.unmountComponentAtNode=function(e){if(!Is(e))throw Error(T(40));return e._reactRootContainer?(an(function(){zs(null,null,e,!1,function(){e._reactRootContainer=null,e[dt]=null})}),!0):!1};Ve.unstable_batchedUpdates=Ra;Ve.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Is(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return zs(e,t,n,!1,r)};Ve.version="18.3.1-next-f1338f8080-20240426";function np(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(np)}catch(e){console.error(e)}}np(),nd.exports=Ve;var av=nd.exports,Ac=av;Vo.createRoot=Ac.createRoot,Vo.hydrateRoot=Ac.hydrateRoot;const Ia=E.createContext({});function za(e){const t=E.useRef(null);return t.current===null&&(t.current=e()),t.current}const Fs=E.createContext(null),Fa=E.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class uv extends E.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function cv({children:e,isPresent:t}){const n=E.useId(),r=E.useRef(null),i=E.useRef({width:0,height:0,top:0,left:0}),{nonce:s}=E.useContext(Fa);return E.useInsertionEffect(()=>{const{width:o,height:l,top:a,left:u}=i.current;if(t||!r.current||!o||!l)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return s&&(c.nonce=s),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${l}px !important;
            top: ${a}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),k.jsx(uv,{isPresent:t,childRef:r,sizeRef:i,children:E.cloneElement(e,{ref:r})})}const fv=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:s,mode:o})=>{const l=za(dv),a=E.useId(),u=E.useCallback(f=>{l.set(f,!0);for(const d of l.values())if(!d)return;r&&r()},[l,r]),c=E.useMemo(()=>({id:a,initial:t,isPresent:n,custom:i,onExitComplete:u,register:f=>(l.set(f,!1),()=>l.delete(f))}),s?[Math.random(),u]:[n,u]);return E.useMemo(()=>{l.forEach((f,d)=>l.set(d,!1))},[n]),E.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),o==="popLayout"&&(e=k.jsx(cv,{isPresent:n,children:e})),k.jsx(Fs.Provider,{value:c,children:e})};function dv(){return new Map}function rp(e=!0){const t=E.useContext(Fs);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:i}=t,s=E.useId();E.useEffect(()=>{e&&i(s)},[e]);const o=E.useCallback(()=>e&&r&&r(s),[s,r,e]);return!n&&r?[!1,o]:[!0]}const Ei=e=>e.key||"";function Rc(e){const t=[];return E.Children.forEach(e,n=>{E.isValidElement(n)&&t.push(n)}),t}const Oa=typeof window<"u",ip=Oa?E.useLayoutEffect:E.useEffect,Ba=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:s="sync",propagate:o=!1})=>{const[l,a]=rp(o),u=E.useMemo(()=>Rc(e),[e]),c=o&&!l?[]:u.map(Ei),f=E.useRef(!0),d=E.useRef(u),g=za(()=>new Map),[v,y]=E.useState(u),[S,h]=E.useState(u);ip(()=>{f.current=!1,d.current=u;for(let w=0;w<S.length;w++){const x=Ei(S[w]);c.includes(x)?g.delete(x):g.get(x)!==!0&&g.set(x,!1)}},[S,c.length,c.join("-")]);const p=[];if(u!==v){let w=[...u];for(let x=0;x<S.length;x++){const C=S[x],A=Ei(C);c.includes(A)||(w.splice(x,0,C),p.push(C))}s==="wait"&&p.length&&(w=p),h(Rc(w)),y(u);return}const{forceRender:m}=E.useContext(Ia);return k.jsx(k.Fragment,{children:S.map(w=>{const x=Ei(w),C=o&&!l?!1:u===S||c.includes(x),A=()=>{if(g.has(x))g.set(x,!0);else return;let P=!0;g.forEach(j=>{j||(P=!1)}),P&&(m==null||m(),h(d.current),o&&(a==null||a()),r&&r())};return k.jsx(fv,{isPresent:C,initial:!f.current||n?void 0:!1,custom:C?void 0:t,presenceAffectsLayout:i,mode:s,onExitComplete:C?void 0:A,children:w},x)})})},Me=e=>e;let sp=Me;function Ua(e){let t;return()=>(t===void 0&&(t=e()),t)}const Hn=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},ut=e=>e*1e3,ct=e=>e/1e3,hv={useManualTiming:!1};function pv(e){let t=new Set,n=new Set,r=!1,i=!1;const s=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function l(u){s.has(u)&&(a.schedule(u),e()),u(o)}const a={schedule:(u,c=!1,f=!1)=>{const g=f&&r?t:n;return c&&s.add(u),g.has(u)||g.add(u),u},cancel:u=>{n.delete(u),s.delete(u)},process:u=>{if(o=u,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(l),t.clear(),r=!1,i&&(i=!1,a.process(u))}};return a}const Ai=["read","resolveKeyframes","update","preRender","render","postRender"],mv=40;function op(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,o=Ai.reduce((h,p)=>(h[p]=pv(s),h),{}),{read:l,resolveKeyframes:a,update:u,preRender:c,render:f,postRender:d}=o,g=()=>{const h=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(h-i.timestamp,mv),1),i.timestamp=h,i.isProcessing=!0,l.process(i),a.process(i),u.process(i),c.process(i),f.process(i),d.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(g))},v=()=>{n=!0,r=!0,i.isProcessing||e(g)};return{schedule:Ai.reduce((h,p)=>{const m=o[p];return h[p]=(w,x=!1,C=!1)=>(n||v(),m.schedule(w,x,C)),h},{}),cancel:h=>{for(let p=0;p<Ai.length;p++)o[Ai[p]].cancel(h)},state:i,steps:o}}const{schedule:U,cancel:It,state:le,steps:vo}=op(typeof requestAnimationFrame<"u"?requestAnimationFrame:Me,!0),lp=E.createContext({strict:!1}),Mc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Gn={};for(const e in Mc)Gn[e]={isEnabled:t=>Mc[e].some(n=>!!t[n])};function gv(e){for(const t in e)Gn[t]={...Gn[t],...e[t]}}const yv=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ms(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||yv.has(e)}let ap=e=>!ms(e);function vv(e){e&&(ap=t=>t.startsWith("on")?!ms(t):e(t))}try{vv(require("@emotion/is-prop-valid").default)}catch{}function wv(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(ap(i)||n===!0&&ms(i)||!t&&!ms(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Sv(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,i)=>i==="create"?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}const Os=E.createContext({});function Gr(e){return typeof e=="string"||Array.isArray(e)}function Bs(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const $a=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Wa=["initial",...$a];function Us(e){return Bs(e.animate)||Wa.some(t=>Gr(e[t]))}function up(e){return!!(Us(e)||e.variants)}function xv(e,t){if(Us(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Gr(n)?n:void 0,animate:Gr(r)?r:void 0}}return e.inherit!==!1?t:{}}function kv(e){const{initial:t,animate:n}=xv(e,E.useContext(Os));return E.useMemo(()=>({initial:t,animate:n}),[Dc(t),Dc(n)])}function Dc(e){return Array.isArray(e)?e.join(" "):e}const Pv=Symbol.for("motionComponentSymbol");function En(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Tv(e,t,n){return E.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):En(n)&&(n.current=r))},[t])}const Ka=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Cv="framerAppearId",cp="data-"+Ka(Cv),{schedule:Ha}=op(queueMicrotask,!1),fp=E.createContext({});function Ev(e,t,n,r,i){var s,o;const{visualElement:l}=E.useContext(Os),a=E.useContext(lp),u=E.useContext(Fs),c=E.useContext(Fa).reducedMotion,f=E.useRef(null);r=r||a.renderer,!f.current&&r&&(f.current=r(e,{visualState:t,parent:l,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const d=f.current,g=E.useContext(fp);d&&!d.projection&&i&&(d.type==="html"||d.type==="svg")&&Av(f.current,n,i,g);const v=E.useRef(!1);E.useInsertionEffect(()=>{d&&v.current&&d.update(n,u)});const y=n[cp],S=E.useRef(!!y&&!(!((s=window.MotionHandoffIsComplete)===null||s===void 0)&&s.call(window,y))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,y)));return ip(()=>{d&&(v.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),Ha.render(d.render),S.current&&d.animationState&&d.animationState.animateChanges())}),E.useEffect(()=>{d&&(!S.current&&d.animationState&&d.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var h;(h=window.MotionHandoffMarkAsComplete)===null||h===void 0||h.call(window,y)}),S.current=!1))}),d}function Av(e,t,n,r){const{layoutId:i,layout:s,drag:o,dragConstraints:l,layoutScroll:a,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:dp(e.parent)),e.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:!!o||l&&En(l),visualElement:e,animationType:typeof s=="string"?s:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:u})}function dp(e){if(e)return e.options.allowProjection!==!1?e.projection:dp(e.parent)}function Rv({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){var s,o;e&&gv(e);function l(u,c){let f;const d={...E.useContext(Fa),...u,layoutId:Mv(u)},{isStatic:g}=d,v=kv(u),y=r(u,g);if(!g&&Oa){Dv();const S=Lv(d);f=S.MeasureLayout,v.visualElement=Ev(i,y,d,t,S.ProjectionNode)}return k.jsxs(Os.Provider,{value:v,children:[f&&v.visualElement?k.jsx(f,{visualElement:v.visualElement,...d}):null,n(i,u,Tv(y,v.visualElement,c),y,g,v.visualElement)]})}l.displayName=`motion.${typeof i=="string"?i:`create(${(o=(s=i.displayName)!==null&&s!==void 0?s:i.name)!==null&&o!==void 0?o:""})`}`;const a=E.forwardRef(l);return a[Pv]=i,a}function Mv({layoutId:e}){const t=E.useContext(Ia).id;return t&&e!==void 0?t+"-"+e:e}function Dv(e,t){E.useContext(lp).strict}function Lv(e){const{drag:t,layout:n}=Gn;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const Vv=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ga(e){return typeof e!="string"||e.includes("-")?!1:!!(Vv.indexOf(e)>-1||/[A-Z]/u.test(e))}function Lc(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Qa(e,t,n,r){if(typeof t=="function"){const[i,s]=Lc(r);t=t(n!==void 0?n:e.custom,i,s)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,s]=Lc(r);t=t(n!==void 0?n:e.custom,i,s)}return t}const Cl=e=>Array.isArray(e),Nv=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),jv=e=>Cl(e)?e[e.length-1]||0:e,me=e=>!!(e&&e.getVelocity);function $i(e){const t=me(e)?e.get():e;return Nv(t)?t.toValue():t}function _v({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,i,s){const o={latestValues:Iv(r,i,s,e),renderState:t()};return n&&(o.onMount=l=>n({props:r,current:l,...o}),o.onUpdate=l=>n(l)),o}const hp=e=>(t,n)=>{const r=E.useContext(Os),i=E.useContext(Fs),s=()=>_v(e,t,r,i);return n?s():za(s)};function Iv(e,t,n,r){const i={},s=r(e,{});for(const d in s)i[d]=$i(s[d]);let{initial:o,animate:l}=e;const a=Us(e),u=up(e);t&&u&&!a&&e.inherit!==!1&&(o===void 0&&(o=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const f=c?l:o;if(f&&typeof f!="boolean"&&!Bs(f)){const d=Array.isArray(f)?f:[f];for(let g=0;g<d.length;g++){const v=Qa(e,d[g]);if(v){const{transitionEnd:y,transition:S,...h}=v;for(const p in h){let m=h[p];if(Array.isArray(m)){const w=c?m.length-1:0;m=m[w]}m!==null&&(i[p]=m)}for(const p in y)i[p]=y[p]}}}return i}const qn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],fn=new Set(qn),pp=e=>t=>typeof t=="string"&&t.startsWith(e),mp=pp("--"),zv=pp("var(--"),Xa=e=>zv(e)?Fv.test(e.split("/*")[0].trim()):!1,Fv=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,gp=(e,t)=>t&&typeof e=="number"?t.transform(e):e,mt=(e,t,n)=>n>t?t:n<e?e:n,Jn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Qr={...Jn,transform:e=>mt(0,1,e)},Ri={...Jn,default:1},ni=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),wt=ni("deg"),nt=ni("%"),D=ni("px"),Ov=ni("vh"),Bv=ni("vw"),Vc={...nt,parse:e=>nt.parse(e)/100,transform:e=>nt.transform(e*100)},Uv={borderWidth:D,borderTopWidth:D,borderRightWidth:D,borderBottomWidth:D,borderLeftWidth:D,borderRadius:D,radius:D,borderTopLeftRadius:D,borderTopRightRadius:D,borderBottomRightRadius:D,borderBottomLeftRadius:D,width:D,maxWidth:D,height:D,maxHeight:D,top:D,right:D,bottom:D,left:D,padding:D,paddingTop:D,paddingRight:D,paddingBottom:D,paddingLeft:D,margin:D,marginTop:D,marginRight:D,marginBottom:D,marginLeft:D,backgroundPositionX:D,backgroundPositionY:D},$v={rotate:wt,rotateX:wt,rotateY:wt,rotateZ:wt,scale:Ri,scaleX:Ri,scaleY:Ri,scaleZ:Ri,skew:wt,skewX:wt,skewY:wt,distance:D,translateX:D,translateY:D,translateZ:D,x:D,y:D,z:D,perspective:D,transformPerspective:D,opacity:Qr,originX:Vc,originY:Vc,originZ:D},Nc={...Jn,transform:Math.round},Ya={...Uv,...$v,zIndex:Nc,size:D,fillOpacity:Qr,strokeOpacity:Qr,numOctaves:Nc},Wv={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Kv=qn.length;function Hv(e,t,n){let r="",i=!0;for(let s=0;s<Kv;s++){const o=qn[s],l=e[o];if(l===void 0)continue;let a=!0;if(typeof l=="number"?a=l===(o.startsWith("scale")?1:0):a=parseFloat(l)===0,!a||n){const u=gp(l,Ya[o]);if(!a){i=!1;const c=Wv[o]||o;r+=`${c}(${u}) `}n&&(t[o]=u)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}function Za(e,t,n){const{style:r,vars:i,transformOrigin:s}=e;let o=!1,l=!1;for(const a in t){const u=t[a];if(fn.has(a)){o=!0;continue}else if(mp(a)){i[a]=u;continue}else{const c=gp(u,Ya[a]);a.startsWith("origin")?(l=!0,s[a]=c):r[a]=c}}if(t.transform||(o||n?r.transform=Hv(t,e.transform,n):r.transform&&(r.transform="none")),l){const{originX:a="50%",originY:u="50%",originZ:c=0}=s;r.transformOrigin=`${a} ${u} ${c}`}}const Gv={offset:"stroke-dashoffset",array:"stroke-dasharray"},Qv={offset:"strokeDashoffset",array:"strokeDasharray"};function Xv(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?Gv:Qv;e[s.offset]=D.transform(-r);const o=D.transform(t),l=D.transform(n);e[s.array]=`${o} ${l}`}function jc(e,t,n){return typeof e=="string"?e:D.transform(t+n*e)}function Yv(e,t,n){const r=jc(t,e.x,e.width),i=jc(n,e.y,e.height);return`${r} ${i}`}function qa(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:l=1,pathOffset:a=0,...u},c,f){if(Za(e,u,f),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:d,style:g,dimensions:v}=e;d.transform&&(v&&(g.transform=d.transform),delete d.transform),v&&(i!==void 0||s!==void 0||g.transform)&&(g.transformOrigin=Yv(v,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(d.x=t),n!==void 0&&(d.y=n),r!==void 0&&(d.scale=r),o!==void 0&&Xv(d,o,l,a,!1)}const Ja=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),yp=()=>({...Ja(),attrs:{}}),ba=e=>typeof e=="string"&&e.toLowerCase()==="svg";function vp(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const wp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Sp(e,t,n,r){vp(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(wp.has(i)?i:Ka(i),t.attrs[i])}const gs={};function Zv(e){Object.assign(gs,e)}function xp(e,{layout:t,layoutId:n}){return fn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!gs[e]||e==="opacity")}function eu(e,t,n){var r;const{style:i}=e,s={};for(const o in i)(me(i[o])||t.style&&me(t.style[o])||xp(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(s[o]=i[o]);return s}function kp(e,t,n){const r=eu(e,t,n);for(const i in e)if(me(e[i])||me(t[i])){const s=qn.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[s]=e[i]}return r}function qv(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const _c=["x","y","width","height","cx","cy","r"],Jv={useVisualState:hp({scrapeMotionValuesFromProps:kp,createRenderState:yp,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let s=!!e.drag;if(!s){for(const l in i)if(fn.has(l)){s=!0;break}}if(!s)return;let o=!t;if(t)for(let l=0;l<_c.length;l++){const a=_c[l];e[a]!==t[a]&&(o=!0)}o&&U.read(()=>{qv(n,r),U.render(()=>{qa(r,i,ba(n.tagName),e.transformTemplate),Sp(n,r)})})}})},bv={useVisualState:hp({scrapeMotionValuesFromProps:eu,createRenderState:Ja})};function Pp(e,t,n){for(const r in t)!me(t[r])&&!xp(r,n)&&(e[r]=t[r])}function e0({transformTemplate:e},t){return E.useMemo(()=>{const n=Ja();return Za(n,t,e),Object.assign({},n.vars,n.style)},[t])}function t0(e,t){const n=e.style||{},r={};return Pp(r,n,e),Object.assign(r,e0(e,t)),r}function n0(e,t){const n={},r=t0(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function r0(e,t,n,r){const i=E.useMemo(()=>{const s=yp();return qa(s,t,ba(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};Pp(s,e.style,e),i.style={...s,...i.style}}return i}function i0(e=!1){return(n,r,i,{latestValues:s},o)=>{const a=(Ga(n)?r0:n0)(r,s,o,n),u=wv(r,typeof n=="string",e),c=n!==E.Fragment?{...u,...a,ref:i}:{},{children:f}=r,d=E.useMemo(()=>me(f)?f.get():f,[f]);return E.createElement(n,{...c,children:d})}}function s0(e,t){return function(r,{forwardMotionProps:i}={forwardMotionProps:!1}){const o={...Ga(r)?Jv:bv,preloadedFeatures:e,useRender:i0(i),createVisualElement:t,Component:r};return Rv(o)}}function Tp(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function $s(e,t,n){const r=e.getProps();return Qa(r,t,n!==void 0?n:r.custom,e)}const o0=Ua(()=>window.ScrollTimeline!==void 0);class l0{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(i=>{if(o0()&&i.attachTimeline)return i.attachTimeline(t);if(typeof n=="function")return n(i)});return()=>{r.forEach((i,s)=>{i&&i(),this.animations[s].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class a0 extends l0{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function tu(e,t){return e?e[t]||e.default||e:void 0}const El=2e4;function Cp(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<El;)t+=n,r=e.next(t);return t>=El?1/0:t}function nu(e){return typeof e=="function"}function Ic(e,t){e.timeline=t,e.onfinish=null}const ru=e=>Array.isArray(e)&&typeof e[0]=="number",u0={linearEasing:void 0};function c0(e,t){const n=Ua(e);return()=>{var r;return(r=u0[t])!==null&&r!==void 0?r:n()}}const ys=c0(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Ep=(e,t,n=10)=>{let r="";const i=Math.max(Math.round(t/n),2);for(let s=0;s<i;s++)r+=e(Hn(0,i-1,s))+", ";return`linear(${r.substring(0,r.length-2)})`};function Ap(e){return!!(typeof e=="function"&&ys()||!e||typeof e=="string"&&(e in Al||ys())||ru(e)||Array.isArray(e)&&e.every(Ap))}const hr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Al={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:hr([0,.65,.55,1]),circOut:hr([.55,0,1,.45]),backIn:hr([.31,.01,.66,-.59]),backOut:hr([.33,1.53,.69,.99])};function Rp(e,t){if(e)return typeof e=="function"&&ys()?Ep(e,t):ru(e)?hr(e):Array.isArray(e)?e.map(n=>Rp(n,t)||Al.easeOut):Al[e]}const Ke={x:!1,y:!1};function Mp(){return Ke.x||Ke.y}function f0(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let i=document;const s=(r=void 0)!==null&&r!==void 0?r:i.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}function Dp(e,t){const n=f0(e),r=new AbortController,i={passive:!0,...t,signal:r.signal};return[n,i,()=>r.abort()]}function zc(e){return t=>{t.pointerType==="touch"||Mp()||e(t)}}function d0(e,t,n={}){const[r,i,s]=Dp(e,n),o=zc(l=>{const{target:a}=l,u=t(l);if(typeof u!="function"||!a)return;const c=zc(f=>{u(f),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,i)});return r.forEach(l=>{l.addEventListener("pointerenter",o,i)}),s}const Lp=(e,t)=>t?e===t?!0:Lp(e,t.parentElement):!1,iu=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,h0=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function p0(e){return h0.has(e.tagName)||e.tabIndex!==-1}const pr=new WeakSet;function Fc(e){return t=>{t.key==="Enter"&&e(t)}}function wo(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const m0=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=Fc(()=>{if(pr.has(n))return;wo(n,"down");const i=Fc(()=>{wo(n,"up")}),s=()=>wo(n,"cancel");n.addEventListener("keyup",i,t),n.addEventListener("blur",s,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Oc(e){return iu(e)&&!Mp()}function g0(e,t,n={}){const[r,i,s]=Dp(e,n),o=l=>{const a=l.currentTarget;if(!Oc(l)||pr.has(a))return;pr.add(a);const u=t(l),c=(g,v)=>{window.removeEventListener("pointerup",f),window.removeEventListener("pointercancel",d),!(!Oc(g)||!pr.has(a))&&(pr.delete(a),typeof u=="function"&&u(g,{success:v}))},f=g=>{c(g,n.useGlobalTarget||Lp(a,g.target))},d=g=>{c(g,!1)};window.addEventListener("pointerup",f,i),window.addEventListener("pointercancel",d,i)};return r.forEach(l=>{!p0(l)&&l.getAttribute("tabindex")===null&&(l.tabIndex=0),(n.useGlobalTarget?window:l).addEventListener("pointerdown",o,i),l.addEventListener("focus",u=>m0(u,i),i)}),s}function y0(e){return e==="x"||e==="y"?Ke[e]?null:(Ke[e]=!0,()=>{Ke[e]=!1}):Ke.x||Ke.y?null:(Ke.x=Ke.y=!0,()=>{Ke.x=Ke.y=!1})}const Vp=new Set(["width","height","top","left","right","bottom",...qn]);let Wi;function v0(){Wi=void 0}const rt={now:()=>(Wi===void 0&&rt.set(le.isProcessing||hv.useManualTiming?le.timestamp:performance.now()),Wi),set:e=>{Wi=e,queueMicrotask(v0)}};function su(e,t){e.indexOf(t)===-1&&e.push(t)}function ou(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class lu{constructor(){this.subscriptions=[]}add(t){return su(this.subscriptions,t),()=>ou(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Np(e,t){return t?e*(1e3/t):0}const Bc=30,w0=e=>!isNaN(parseFloat(e));class S0{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,i=!0)=>{const s=rt.now();this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=rt.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=w0(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new lu);const r=this.events[t].add(n);return t==="change"?()=>{r(),U.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=rt.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Bc)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Bc);return Np(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Xr(e,t){return new S0(e,t)}function x0(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Xr(n))}function k0(e,t){const n=$s(e,t);let{transitionEnd:r={},transition:i={},...s}=n||{};s={...s,...r};for(const o in s){const l=jv(s[o]);x0(e,o,l)}}function P0(e){return!!(me(e)&&e.add)}function Rl(e,t){const n=e.getValue("willChange");if(P0(n))return n.add(t)}function jp(e){return e.props[cp]}const _p=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,T0=1e-7,C0=12;function E0(e,t,n,r,i){let s,o,l=0;do o=t+(n-t)/2,s=_p(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>T0&&++l<C0);return o}function ri(e,t,n,r){if(e===t&&n===r)return Me;const i=s=>E0(s,0,1,e,n);return s=>s===0||s===1?s:_p(i(s),t,r)}const Ip=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,zp=e=>t=>1-e(1-t),Fp=ri(.33,1.53,.69,.99),au=zp(Fp),Op=Ip(au),Bp=e=>(e*=2)<1?.5*au(e):.5*(2-Math.pow(2,-10*(e-1))),uu=e=>1-Math.sin(Math.acos(e)),Up=zp(uu),$p=Ip(uu),Wp=e=>/^0[^.\s]+$/u.test(e);function A0(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Wp(e):!0}const Cr=e=>Math.round(e*1e5)/1e5,cu=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function R0(e){return e==null}const M0=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,fu=(e,t)=>n=>!!(typeof n=="string"&&M0.test(n)&&n.startsWith(e)||t&&!R0(n)&&Object.prototype.hasOwnProperty.call(n,t)),Kp=(e,t,n)=>r=>{if(typeof r!="string")return r;const[i,s,o,l]=r.match(cu);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:l!==void 0?parseFloat(l):1}},D0=e=>mt(0,255,e),So={...Jn,transform:e=>Math.round(D0(e))},bt={test:fu("rgb","red"),parse:Kp("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+So.transform(e)+", "+So.transform(t)+", "+So.transform(n)+", "+Cr(Qr.transform(r))+")"};function L0(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Ml={test:fu("#"),parse:L0,transform:bt.transform},An={test:fu("hsl","hue"),parse:Kp("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+nt.transform(Cr(t))+", "+nt.transform(Cr(n))+", "+Cr(Qr.transform(r))+")"},he={test:e=>bt.test(e)||Ml.test(e)||An.test(e),parse:e=>bt.test(e)?bt.parse(e):An.test(e)?An.parse(e):Ml.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?bt.transform(e):An.transform(e)},V0=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function N0(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(cu))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(V0))===null||n===void 0?void 0:n.length)||0)>0}const Hp="number",Gp="color",j0="var",_0="var(",Uc="${}",I0=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Yr(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let s=0;const l=t.replace(I0,a=>(he.test(a)?(r.color.push(s),i.push(Gp),n.push(he.parse(a))):a.startsWith(_0)?(r.var.push(s),i.push(j0),n.push(a)):(r.number.push(s),i.push(Hp),n.push(parseFloat(a))),++s,Uc)).split(Uc);return{values:n,split:l,indexes:r,types:i}}function Qp(e){return Yr(e).values}function Xp(e){const{split:t,types:n}=Yr(e),r=t.length;return i=>{let s="";for(let o=0;o<r;o++)if(s+=t[o],i[o]!==void 0){const l=n[o];l===Hp?s+=Cr(i[o]):l===Gp?s+=he.transform(i[o]):s+=i[o]}return s}}const z0=e=>typeof e=="number"?0:e;function F0(e){const t=Qp(e);return Xp(e)(t.map(z0))}const zt={test:N0,parse:Qp,createTransformer:Xp,getAnimatableNone:F0},O0=new Set(["brightness","contrast","saturate","opacity"]);function B0(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(cu)||[];if(!r)return e;const i=n.replace(r,"");let s=O0.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const U0=/\b([a-z-]*)\(.*?\)/gu,Dl={...zt,getAnimatableNone:e=>{const t=e.match(U0);return t?t.map(B0).join(" "):e}},$0={...Ya,color:he,backgroundColor:he,outlineColor:he,fill:he,stroke:he,borderColor:he,borderTopColor:he,borderRightColor:he,borderBottomColor:he,borderLeftColor:he,filter:Dl,WebkitFilter:Dl},du=e=>$0[e];function Yp(e,t){let n=du(e);return n!==Dl&&(n=zt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const W0=new Set(["auto","none","0"]);function K0(e,t,n){let r=0,i;for(;r<e.length&&!i;){const s=e[r];typeof s=="string"&&!W0.has(s)&&Yr(s).values.length&&(i=e[r]),r++}if(i&&n)for(const s of t)e[s]=Yp(n,i)}const $c=e=>e===Jn||e===D,Wc=(e,t)=>parseFloat(e.split(", ")[t]),Kc=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/u);if(i)return Wc(i[1],t);{const s=r.match(/^matrix\((.+)\)$/u);return s?Wc(s[1],e):0}},H0=new Set(["x","y","z"]),G0=qn.filter(e=>!H0.has(e));function Q0(e){const t=[];return G0.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Qn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Kc(4,13),y:Kc(5,14)};Qn.translateX=Qn.x;Qn.translateY=Qn.y;const nn=new Set;let Ll=!1,Vl=!1;function Zp(){if(Vl){const e=Array.from(nn).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const i=Q0(r);i.length&&(n.set(r,i),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const i=n.get(r);i&&i.forEach(([s,o])=>{var l;(l=r.getValue(s))===null||l===void 0||l.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Vl=!1,Ll=!1,nn.forEach(e=>e.complete()),nn.clear()}function qp(){nn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Vl=!0)})}function X0(){qp(),Zp()}class hu{constructor(t,n,r,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(nn.add(this),Ll||(Ll=!0,U.read(qp),U.resolveKeyframes(Zp))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:i}=this;for(let s=0;s<t.length;s++)if(t[s]===null)if(s===0){const o=i==null?void 0:i.get(),l=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const a=r.readValue(n,l);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=l),i&&o===void 0&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),nn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,nn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Jp=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Y0=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Z0(e){const t=Y0.exec(e);if(!t)return[,];const[,n,r,i]=t;return[`--${n??r}`,i]}function bp(e,t,n=1){const[r,i]=Z0(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return Jp(o)?parseFloat(o):o}return Xa(i)?bp(i,t,n+1):i}const em=e=>t=>t.test(e),q0={test:e=>e==="auto",parse:e=>e},tm=[Jn,D,nt,wt,Bv,Ov,q0],Hc=e=>tm.find(em(e));class nm extends hu{constructor(t,n,r,i,s){super(t,n,r,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="string"&&(u=u.trim(),Xa(u))){const c=bp(u,n.current);c!==void 0&&(t[a]=c),a===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Vp.has(r)||t.length!==2)return;const[i,s]=t,o=Hc(i),l=Hc(s);if(o!==l)if($c(o)&&$c(l))for(let a=0;a<t.length;a++){const u=t[a];typeof u=="string"&&(t[a]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let i=0;i<t.length;i++)A0(t[i])&&r.push(i);r.length&&K0(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Qn[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&t.getValue(r,i).jump(i,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const s=n.getValue(r);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,l=i[o];i[o]=Qn[r](n.measureViewportBox(),window.getComputedStyle(n.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([a,u])=>{n.getValue(a).set(u)}),this.resolveNoneKeyframes()}}const Gc=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(zt.test(e)||e==="0")&&!e.startsWith("url("));function J0(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function b0(e,t,n,r){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const s=e[e.length-1],o=Gc(i,t),l=Gc(s,t);return!o||!l?!1:J0(e)||(n==="spring"||nu(n))&&r}const e1=e=>e!==null;function Ws(e,{repeat:t,repeatType:n="loop"},r){const i=e.filter(e1),s=t&&n!=="loop"&&t%2===1?0:i.length-1;return!s||r===void 0?i[s]:r}const t1=40;class rm{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=rt.now(),this.options={autoplay:t,delay:n,type:r,repeat:i,repeatDelay:s,repeatType:o,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>t1?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&X0(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=rt.now(),this.hasAttemptedResolve=!0;const{name:r,type:i,velocity:s,delay:o,onComplete:l,onUpdate:a,isGenerator:u}=this.options;if(!u&&!b0(t,r,i,s))if(o)this.options.duration=0;else{a&&a(Ws(t,this.options,n)),l&&l(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const K=(e,t,n)=>e+(t-e)*n;function xo(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function n1({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=xo(a,l,e+1/3),s=xo(a,l,e),o=xo(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}function vs(e,t){return n=>n>0?t:e}const ko=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},r1=[Ml,bt,An],i1=e=>r1.find(t=>t.test(e));function Qc(e){const t=i1(e);if(!t)return!1;let n=t.parse(e);return t===An&&(n=n1(n)),n}const Xc=(e,t)=>{const n=Qc(e),r=Qc(t);if(!n||!r)return vs(e,t);const i={...n};return s=>(i.red=ko(n.red,r.red,s),i.green=ko(n.green,r.green,s),i.blue=ko(n.blue,r.blue,s),i.alpha=K(n.alpha,r.alpha,s),bt.transform(i))},s1=(e,t)=>n=>t(e(n)),ii=(...e)=>e.reduce(s1),Nl=new Set(["none","hidden"]);function o1(e,t){return Nl.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function l1(e,t){return n=>K(e,t,n)}function pu(e){return typeof e=="number"?l1:typeof e=="string"?Xa(e)?vs:he.test(e)?Xc:c1:Array.isArray(e)?im:typeof e=="object"?he.test(e)?Xc:a1:vs}function im(e,t){const n=[...e],r=n.length,i=e.map((s,o)=>pu(s)(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}}function a1(e,t){const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=pu(e[i])(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}}function u1(e,t){var n;const r=[],i={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],l=e.indexes[o][i[o]],a=(n=e.values[l])!==null&&n!==void 0?n:0;r[s]=a,i[o]++}return r}const c1=(e,t)=>{const n=zt.createTransformer(t),r=Yr(e),i=Yr(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?Nl.has(e)&&!i.values.length||Nl.has(t)&&!r.values.length?o1(e,t):ii(im(u1(r,i),i.values),n):vs(e,t)};function sm(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?K(e,t,n):pu(e)(e,t)}const f1=5;function om(e,t,n){const r=Math.max(t-f1,0);return Np(n-e(r),t-r)}const Q={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Po=.001;function d1({duration:e=Q.duration,bounce:t=Q.bounce,velocity:n=Q.velocity,mass:r=Q.mass}){let i,s,o=1-t;o=mt(Q.minDamping,Q.maxDamping,o),e=mt(Q.minDuration,Q.maxDuration,ct(e)),o<1?(i=u=>{const c=u*o,f=c*e,d=c-n,g=jl(u,o),v=Math.exp(-f);return Po-d/g*v},s=u=>{const f=u*o*e,d=f*n+n,g=Math.pow(o,2)*Math.pow(u,2)*e,v=Math.exp(-f),y=jl(Math.pow(u,2),o);return(-i(u)+Po>0?-1:1)*((d-g)*v)/y}):(i=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-Po+c*f},s=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=p1(i,s,l);if(e=ut(e),isNaN(a))return{stiffness:Q.stiffness,damping:Q.damping,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const h1=12;function p1(e,t,n){let r=n;for(let i=1;i<h1;i++)r=r-e(r)/t(r);return r}function jl(e,t){return e*Math.sqrt(1-t*t)}const m1=["duration","bounce"],g1=["stiffness","damping","mass"];function Yc(e,t){return t.some(n=>e[n]!==void 0)}function y1(e){let t={velocity:Q.velocity,stiffness:Q.stiffness,damping:Q.damping,mass:Q.mass,isResolvedFromDuration:!1,...e};if(!Yc(e,g1)&&Yc(e,m1))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),i=r*r,s=2*mt(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:Q.mass,stiffness:i,damping:s}}else{const n=d1(e);t={...t,...n,mass:Q.mass},t.isResolvedFromDuration=!0}return t}function lm(e=Q.visualDuration,t=Q.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:i}=n;const s=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:s},{stiffness:a,damping:u,mass:c,duration:f,velocity:d,isResolvedFromDuration:g}=y1({...n,velocity:-ct(n.velocity||0)}),v=d||0,y=u/(2*Math.sqrt(a*c)),S=o-s,h=ct(Math.sqrt(a/c)),p=Math.abs(S)<5;r||(r=p?Q.restSpeed.granular:Q.restSpeed.default),i||(i=p?Q.restDelta.granular:Q.restDelta.default);let m;if(y<1){const x=jl(h,y);m=C=>{const A=Math.exp(-y*h*C);return o-A*((v+y*h*S)/x*Math.sin(x*C)+S*Math.cos(x*C))}}else if(y===1)m=x=>o-Math.exp(-h*x)*(S+(v+h*S)*x);else{const x=h*Math.sqrt(y*y-1);m=C=>{const A=Math.exp(-y*h*C),P=Math.min(x*C,300);return o-A*((v+y*h*S)*Math.sinh(P)+x*S*Math.cosh(P))/x}}const w={calculatedDuration:g&&f||null,next:x=>{const C=m(x);if(g)l.done=x>=f;else{let A=0;y<1&&(A=x===0?ut(v):om(m,x,C));const P=Math.abs(A)<=r,j=Math.abs(o-C)<=i;l.done=P&&j}return l.value=l.done?o:C,l},toString:()=>{const x=Math.min(Cp(w),El),C=Ep(A=>w.next(x*A).value,x,30);return x+"ms "+C}};return w}function Zc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:l,max:a,restDelta:u=.5,restSpeed:c}){const f=e[0],d={done:!1,value:f},g=P=>l!==void 0&&P<l||a!==void 0&&P>a,v=P=>l===void 0?a:a===void 0||Math.abs(l-P)<Math.abs(a-P)?l:a;let y=n*t;const S=f+y,h=o===void 0?S:o(S);h!==S&&(y=h-f);const p=P=>-y*Math.exp(-P/r),m=P=>h+p(P),w=P=>{const j=p(P),L=m(P);d.done=Math.abs(j)<=u,d.value=d.done?h:L};let x,C;const A=P=>{g(d.value)&&(x=P,C=lm({keyframes:[d.value,v(d.value)],velocity:om(m,P,d.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return A(0),{calculatedDuration:null,next:P=>{let j=!1;return!C&&x===void 0&&(j=!0,w(P),A(P)),x!==void 0&&P>=x?C.next(P-x):(!j&&w(P),d)}}}const v1=ri(.42,0,1,1),w1=ri(0,0,.58,1),am=ri(.42,0,.58,1),S1=e=>Array.isArray(e)&&typeof e[0]!="number",x1={linear:Me,easeIn:v1,easeInOut:am,easeOut:w1,circIn:uu,circInOut:$p,circOut:Up,backIn:au,backInOut:Op,backOut:Fp,anticipate:Bp},qc=e=>{if(ru(e)){sp(e.length===4);const[t,n,r,i]=e;return ri(t,n,r,i)}else if(typeof e=="string")return x1[e];return e};function k1(e,t,n){const r=[],i=n||sm,s=e.length-1;for(let o=0;o<s;o++){let l=i(e[o],e[o+1]);if(t){const a=Array.isArray(t)?t[o]||Me:t;l=ii(a,l)}r.push(l)}return r}function P1(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(sp(s===t.length),s===1)return()=>t[0];if(s===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const l=k1(t,r,i),a=l.length,u=c=>{if(o&&c<e[0])return t[0];let f=0;if(a>1)for(;f<e.length-2&&!(c<e[f+1]);f++);const d=Hn(e[f],e[f+1],c);return l[f](d)};return n?c=>u(mt(e[0],e[s-1],c)):u}function T1(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Hn(0,t,r);e.push(K(n,1,i))}}function C1(e){const t=[0];return T1(t,e.length-1),t}function E1(e,t){return e.map(n=>n*t)}function A1(e,t){return e.map(()=>t||am).splice(0,e.length-1)}function ws({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=S1(r)?r.map(qc):qc(r),s={done:!1,value:t[0]},o=E1(n&&n.length===t.length?n:C1(t),e),l=P1(o,t,{ease:Array.isArray(i)?i:A1(t,i)});return{calculatedDuration:e,next:a=>(s.value=l(a),s.done=a>=e,s)}}const R1=e=>{const t=({timestamp:n})=>e(n);return{start:()=>U.update(t,!0),stop:()=>It(t),now:()=>le.isProcessing?le.timestamp:rt.now()}},M1={decay:Zc,inertia:Zc,tween:ws,keyframes:ws,spring:lm},D1=e=>e/100;class mu extends rm{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:a}=this.options;a&&a()};const{name:n,motionValue:r,element:i,keyframes:s}=this.options,o=(i==null?void 0:i.KeyframeResolver)||hu,l=(a,u)=>this.onKeyframesResolved(a,u);this.resolver=new o(s,l,n,r,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,l=nu(n)?n:M1[n]||ws;let a,u;l!==ws&&typeof t[0]!="number"&&(a=ii(D1,sm(t[0],t[1])),t=[0,100]);const c=l({...this.options,keyframes:t});s==="mirror"&&(u=l({...this.options,keyframes:[...t].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=Cp(c));const{calculatedDuration:f}=c,d=f+i,g=d*(r+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:a,calculatedDuration:f,resolvedDuration:d,totalDuration:g}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:P}=this.options;return{done:!0,value:P[P.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:l,keyframes:a,calculatedDuration:u,totalDuration:c,resolvedDuration:f}=r;if(this.startTime===null)return s.next(0);const{delay:d,repeat:g,repeatType:v,repeatDelay:y,onUpdate:S}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const h=this.currentTime-d*(this.speed>=0?1:-1),p=this.speed>=0?h<0:h>c;this.currentTime=Math.max(h,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let m=this.currentTime,w=s;if(g){const P=Math.min(this.currentTime,c)/f;let j=Math.floor(P),L=P%1;!L&&P>=1&&(L=1),L===1&&j--,j=Math.min(j,g+1),!!(j%2)&&(v==="reverse"?(L=1-L,y&&(L-=y/f)):v==="mirror"&&(w=o)),m=mt(0,1,L)*f}const x=p?{done:!1,value:a[0]}:w.next(m);l&&(x.value=l(x.value));let{done:C}=x;!p&&u!==null&&(C=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const A=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&C);return A&&i!==void 0&&(x.value=Ws(a,this.options,i)),S&&S(x.value),A&&this.finish(),x}get duration(){const{resolved:t}=this;return t?ct(t.calculatedDuration):0}get time(){return ct(this.currentTime)}set time(t){t=ut(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=ct(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=R1,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(s=>this.tick(s))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const L1=new Set(["opacity","clipPath","filter","transform"]);function V1(e,t,n,{delay:r=0,duration:i=300,repeat:s=0,repeatType:o="loop",ease:l="easeInOut",times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=Rp(l,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}const N1=Ua(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ss=10,j1=2e4;function _1(e){return nu(e.type)||e.type==="spring"||!Ap(e.ease)}function I1(e,t){const n=new mu({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const i=[];let s=0;for(;!r.done&&s<j1;)r=n.sample(s),i.push(r.value),s+=Ss;return{times:void 0,keyframes:i,duration:s-Ss,ease:"linear"}}const um={anticipate:Bp,backInOut:Op,circInOut:$p};function z1(e){return e in um}class Jc extends rm{constructor(t){super(t);const{name:n,motionValue:r,element:i,keyframes:s}=this.options;this.resolver=new nm(s,(o,l)=>this.onKeyframesResolved(o,l),n,r,i),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:i,ease:s,type:o,motionValue:l,name:a,startTime:u}=this.options;if(!l.owner||!l.owner.current)return!1;if(typeof s=="string"&&ys()&&z1(s)&&(s=um[s]),_1(this.options)){const{onComplete:f,onUpdate:d,motionValue:g,element:v,...y}=this.options,S=I1(t,y);t=S.keyframes,t.length===1&&(t[1]=t[0]),r=S.duration,i=S.times,s=S.ease,o="keyframes"}const c=V1(l.owner.current,a,t,{...this.options,duration:r,times:i,ease:s});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?(Ic(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:f}=this.options;l.set(Ws(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:i,type:o,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return ct(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return ct(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=ut(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return Me;const{animation:r}=n;Ic(r,t)}return Me}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:i,type:s,ease:o,times:l}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:f,element:d,...g}=this.options,v=new mu({...g,keyframes:r,duration:i,type:s,ease:o,times:l,isGenerator:!0}),y=ut(this.time);u.setWithVelocity(v.sample(y-Ss).value,v.sample(y).value,Ss)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:i,repeatType:s,damping:o,type:l}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:u}=n.owner.getProps();return N1()&&r&&L1.has(r)&&!a&&!u&&!i&&s!=="mirror"&&o!==0&&l!=="inertia"}}const F1={type:"spring",stiffness:500,damping:25,restSpeed:10},O1=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),B1={type:"keyframes",duration:.8},U1={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},$1=(e,{keyframes:t})=>t.length>2?B1:fn.has(e)?e.startsWith("scale")?O1(t[1]):F1:U1;function W1({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}const gu=(e,t,n,r={},i,s)=>o=>{const l=tu(r,e)||{},a=l.delay||r.delay||0;let{elapsed:u=0}=r;u=u-ut(a);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-u,onUpdate:d=>{t.set(d),l.onUpdate&&l.onUpdate(d)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:s?void 0:i};W1(l)||(c={...c,...$1(e,c)}),c.duration&&(c.duration=ut(c.duration)),c.repeatDelay&&(c.repeatDelay=ut(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let f=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(f=!0)),f&&!s&&t.get()!==void 0){const d=Ws(c.keyframes,l);if(d!==void 0)return U.update(()=>{c.onUpdate(d),c.onComplete()}),new a0([])}return!s&&Jc.supports(c)?new Jc(c):new mu(c)};function K1({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function cm(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var s;let{transition:o=e.getDefaultTransition(),transitionEnd:l,...a}=t;r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const f in a){const d=e.getValue(f,(s=e.latestValues[f])!==null&&s!==void 0?s:null),g=a[f];if(g===void 0||c&&K1(c,f))continue;const v={delay:n,...tu(o||{},f)};let y=!1;if(window.MotionHandoffAnimation){const h=jp(e);if(h){const p=window.MotionHandoffAnimation(h,f,U);p!==null&&(v.startTime=p,y=!0)}}Rl(e,f),d.start(gu(f,d,g,e.shouldReduceMotion&&Vp.has(f)?{type:!1}:v,e,y));const S=d.animation;S&&u.push(S)}return l&&Promise.all(u).then(()=>{U.update(()=>{l&&k0(e,l)})}),u}function _l(e,t,n={}){var r;const i=$s(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(cm(e,i,n)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:d}=s;return H1(e,t,c+u,f,d,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[u,c]=a==="beforeChildren"?[o,l]:[l,o];return u().then(()=>c())}else return Promise.all([o(),l(n.delay)])}function H1(e,t,n=0,r=0,i=1,s){const o=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(G1).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(_l(u,t,{...s,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function G1(e,t){return e.sortNodePosition(t)}function Q1(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>_l(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=_l(e,t,n);else{const i=typeof t=="function"?$s(e,t,n.custom):t;r=Promise.all(cm(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const X1=Wa.length;function fm(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?fm(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<X1;n++){const r=Wa[n],i=e.props[r];(Gr(i)||i===!1)&&(t[r]=i)}return t}const Y1=[...$a].reverse(),Z1=$a.length;function q1(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Q1(e,n,r)))}function J1(e){let t=q1(e),n=bc(),r=!0;const i=a=>(u,c)=>{var f;const d=$s(e,c,a==="exit"?(f=e.presenceContext)===null||f===void 0?void 0:f.custom:void 0);if(d){const{transition:g,transitionEnd:v,...y}=d;u={...u,...y,...v}}return u};function s(a){t=a(e)}function o(a){const{props:u}=e,c=fm(e.parent)||{},f=[],d=new Set;let g={},v=1/0;for(let S=0;S<Z1;S++){const h=Y1[S],p=n[h],m=u[h]!==void 0?u[h]:c[h],w=Gr(m),x=h===a?p.isActive:null;x===!1&&(v=S);let C=m===c[h]&&m!==u[h]&&w;if(C&&r&&e.manuallyAnimateOnMount&&(C=!1),p.protectedKeys={...g},!p.isActive&&x===null||!m&&!p.prevProp||Bs(m)||typeof m=="boolean")continue;const A=b1(p.prevProp,m);let P=A||h===a&&p.isActive&&!C&&w||S>v&&w,j=!1;const L=Array.isArray(m)?m:[m];let ne=L.reduce(i(h),{});x===!1&&(ne={});const{prevResolvedValues:yt={}}=p,$t={...yt,...ne},bn=b=>{P=!0,d.has(b)&&(j=!0,d.delete(b)),p.needsAnimating[b]=!0;const R=e.getValue(b);R&&(R.liveStyle=!1)};for(const b in $t){const R=ne[b],V=yt[b];if(g.hasOwnProperty(b))continue;let N=!1;Cl(R)&&Cl(V)?N=!Tp(R,V):N=R!==V,N?R!=null?bn(b):d.add(b):R!==void 0&&d.has(b)?bn(b):p.protectedKeys[b]=!0}p.prevProp=m,p.prevResolvedValues=ne,p.isActive&&(g={...g,...ne}),r&&e.blockInitialAnimation&&(P=!1),P&&(!(C&&A)||j)&&f.push(...L.map(b=>({animation:b,options:{type:h}})))}if(d.size){const S={};d.forEach(h=>{const p=e.getBaseTarget(h),m=e.getValue(h);m&&(m.liveStyle=!0),S[h]=p??null}),f.push({animation:S})}let y=!!f.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(y=!1),r=!1,y?t(f):Promise.resolve()}function l(a,u){var c;if(n[a].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(d=>{var g;return(g=d.animationState)===null||g===void 0?void 0:g.setActive(a,u)}),n[a].isActive=u;const f=o(a);for(const d in n)n[d].protectedKeys={};return f}return{animateChanges:o,setActive:l,setAnimateFunction:s,getState:()=>n,reset:()=>{n=bc(),r=!0}}}function b1(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Tp(t,e):!1}function Ht(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function bc(){return{animate:Ht(!0),whileInView:Ht(),whileHover:Ht(),whileTap:Ht(),whileDrag:Ht(),whileFocus:Ht(),exit:Ht()}}class Ut{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ew extends Ut{constructor(t){super(t),t.animationState||(t.animationState=J1(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Bs(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let tw=0;class nw extends Ut{constructor(){super(...arguments),this.id=tw++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const i=this.node.animationState.setActive("exit",!t);n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const rw={animation:{Feature:ew},exit:{Feature:nw}};function Zr(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function si(e){return{point:{x:e.pageX,y:e.pageY}}}const iw=e=>t=>iu(t)&&e(t,si(t));function Er(e,t,n,r){return Zr(e,t,iw(n),r)}const ef=(e,t)=>Math.abs(e-t);function sw(e,t){const n=ef(e.x,t.x),r=ef(e.y,t.y);return Math.sqrt(n**2+r**2)}class dm{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=Co(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,g=sw(f.offset,{x:0,y:0})>=3;if(!d&&!g)return;const{point:v}=f,{timestamp:y}=le;this.history.push({...v,timestamp:y});const{onStart:S,onMove:h}=this.handlers;d||(S&&S(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),h&&h(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=To(d,this.transformPagePoint),U.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=Co(f.type==="pointercancel"?this.lastMoveEventInfo:To(d,this.transformPagePoint),this.history);this.startEvent&&g&&g(f,S),v&&v(f,S)},!iu(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=si(t),l=To(o,this.transformPagePoint),{point:a}=l,{timestamp:u}=le;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Co(l,this.history)),this.removeListeners=ii(Er(this.contextWindow,"pointermove",this.handlePointerMove),Er(this.contextWindow,"pointerup",this.handlePointerUp),Er(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),It(this.updatePoint)}}function To(e,t){return t?{point:t(e.point)}:e}function tf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Co({point:e},t){return{point:e,delta:tf(e,hm(t)),offset:tf(e,ow(t)),velocity:lw(t,.1)}}function ow(e){return e[0]}function hm(e){return e[e.length-1]}function lw(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=hm(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ut(t)));)n--;if(!r)return{x:0,y:0};const s=ct(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const pm=1e-4,aw=1-pm,uw=1+pm,mm=.01,cw=0-mm,fw=0+mm;function Le(e){return e.max-e.min}function dw(e,t,n){return Math.abs(e-t)<=n}function nf(e,t,n,r=.5){e.origin=r,e.originPoint=K(t.min,t.max,e.origin),e.scale=Le(n)/Le(t),e.translate=K(n.min,n.max,e.origin)-e.originPoint,(e.scale>=aw&&e.scale<=uw||isNaN(e.scale))&&(e.scale=1),(e.translate>=cw&&e.translate<=fw||isNaN(e.translate))&&(e.translate=0)}function Ar(e,t,n,r){nf(e.x,t.x,n.x,r?r.originX:void 0),nf(e.y,t.y,n.y,r?r.originY:void 0)}function rf(e,t,n){e.min=n.min+t.min,e.max=e.min+Le(t)}function hw(e,t,n){rf(e.x,t.x,n.x),rf(e.y,t.y,n.y)}function sf(e,t,n){e.min=t.min-n.min,e.max=e.min+Le(t)}function Rr(e,t,n){sf(e.x,t.x,n.x),sf(e.y,t.y,n.y)}function pw(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?K(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?K(n,e,r.max):Math.min(e,n)),e}function of(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function mw(e,{top:t,left:n,bottom:r,right:i}){return{x:of(e.x,n,i),y:of(e.y,t,r)}}function lf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function gw(e,t){return{x:lf(e.x,t.x),y:lf(e.y,t.y)}}function yw(e,t){let n=.5;const r=Le(e),i=Le(t);return i>r?n=Hn(t.min,t.max-r,e.min):r>i&&(n=Hn(e.min,e.max-i,t.min)),mt(0,1,n)}function vw(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Il=.35;function ww(e=Il){return e===!1?e=0:e===!0&&(e=Il),{x:af(e,"left","right"),y:af(e,"top","bottom")}}function af(e,t,n){return{min:uf(e,t),max:uf(e,n)}}function uf(e,t){return typeof e=="number"?e:e[t]||0}const cf=()=>({translate:0,scale:1,origin:0,originPoint:0}),Rn=()=>({x:cf(),y:cf()}),ff=()=>({min:0,max:0}),Z=()=>({x:ff(),y:ff()});function _e(e){return[e("x"),e("y")]}function gm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Sw({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function xw(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Eo(e){return e===void 0||e===1}function zl({scale:e,scaleX:t,scaleY:n}){return!Eo(e)||!Eo(t)||!Eo(n)}function Xt(e){return zl(e)||ym(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function ym(e){return df(e.x)||df(e.y)}function df(e){return e&&e!=="0%"}function xs(e,t,n){const r=e-n,i=t*r;return n+i}function hf(e,t,n,r,i){return i!==void 0&&(e=xs(e,i,r)),xs(e,n,r)+t}function Fl(e,t=0,n=1,r,i){e.min=hf(e.min,t,n,r,i),e.max=hf(e.max,t,n,r,i)}function vm(e,{x:t,y:n}){Fl(e.x,t.translate,t.scale,t.originPoint),Fl(e.y,n.translate,n.scale,n.originPoint)}const pf=.999999999999,mf=1.0000000000001;function kw(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let l=0;l<i;l++){s=n[l],o=s.projectionDelta;const{visualElement:a}=s.options;a&&a.props.style&&a.props.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&Dn(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,vm(e,o)),r&&Xt(s.latestValues)&&Dn(e,s.latestValues))}t.x<mf&&t.x>pf&&(t.x=1),t.y<mf&&t.y>pf&&(t.y=1)}function Mn(e,t){e.min=e.min+t,e.max=e.max+t}function gf(e,t,n,r,i=.5){const s=K(e.min,e.max,i);Fl(e,t,n,s,r)}function Dn(e,t){gf(e.x,t.x,t.scaleX,t.scale,t.originX),gf(e.y,t.y,t.scaleY,t.scale,t.originY)}function wm(e,t){return gm(xw(e.getBoundingClientRect(),t))}function Pw(e,t,n){const r=wm(e,n),{scroll:i}=t;return i&&(Mn(r.x,i.offset.x),Mn(r.y,i.offset.y)),r}const Sm=({current:e})=>e?e.ownerDocument.defaultView:null,Tw=new WeakMap;class Cw{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Z(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(si(c).point)},s=(c,f)=>{const{drag:d,dragPropagation:g,onDragStart:v}=this.getProps();if(d&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=y0(d),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),_e(S=>{let h=this.getAxisMotionValue(S).get()||0;if(nt.test(h)){const{projection:p}=this.visualElement;if(p&&p.layout){const m=p.layout.layoutBox[S];m&&(h=Le(m)*(parseFloat(h)/100))}}this.originPoint[S]=h}),v&&U.postRender(()=>v(c,f)),Rl(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},o=(c,f)=>{const{dragPropagation:d,dragDirectionLock:g,onDirectionLock:v,onDrag:y}=this.getProps();if(!d&&!this.openDragLock)return;const{offset:S}=f;if(g&&this.currentDirection===null){this.currentDirection=Ew(S),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",f.point,S),this.updateAxis("y",f.point,S),this.visualElement.render(),y&&y(c,f)},l=(c,f)=>this.stop(c,f),a=()=>_e(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new dm(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Sm(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&U.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Mi(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=pw(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&En(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=mw(i.layoutBox,n):this.constraints=!1,this.elastic=ww(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&_e(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=vw(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!En(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=Pw(r,i.root,this.visualElement.getTransformPagePoint());let o=gw(i.layout.layoutBox,s);if(n){const l=n(Sw(o));this.hasMutatedConstraints=!!l,l&&(o=gm(l))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=_e(c=>{if(!Mi(c,n,this.currentDirection))return;let f=a&&a[c]||{};o&&(f={min:0,max:0});const d=i?200:1e6,g=i?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...s,...f};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Rl(this.visualElement,t),r.start(gu(t,r,0,n,this.visualElement,!1))}stopAnimation(){_e(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){_e(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){_e(n=>{const{drag:r}=this.getProps();if(!Mi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:l}=i.layout.layoutBox[n];s.set(t[n]-K(o,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!En(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};_e(o=>{const l=this.getAxisMotionValue(o);if(l&&this.constraints!==!1){const a=l.get();i[o]=yw({min:a,max:a},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),_e(o=>{if(!Mi(o,t,null))return;const l=this.getAxisMotionValue(o),{min:a,max:u}=this.constraints[o];l.set(K(a,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;Tw.set(this.visualElement,this);const t=this.visualElement.current,n=Er(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();En(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),U.read(r);const o=Zr(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(_e(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=a[c].translate,f.set(f.get()+a[c].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Il,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:l}}}function Mi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Ew(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class Aw extends Ut{constructor(t){super(t),this.removeGroupControls=Me,this.removeListeners=Me,this.controls=new Cw(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Me}unmount(){this.removeGroupControls(),this.removeListeners()}}const yf=e=>(t,n)=>{e&&U.postRender(()=>e(t,n))};class Rw extends Ut{constructor(){super(...arguments),this.removePointerDownListener=Me}onPointerDown(t){this.session=new dm(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Sm(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:yf(t),onStart:yf(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&U.postRender(()=>i(s,o))}}}mount(){this.removePointerDownListener=Er(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ki={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function vf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ar={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(D.test(e))e=parseFloat(e);else return e;const n=vf(e,t.target.x),r=vf(e,t.target.y);return`${n}% ${r}%`}},Mw={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=zt.parse(e);if(i.length>5)return r;const s=zt.createTransformer(e),o=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+o]/=l,i[1+o]/=a;const u=K(l,a,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}};class Dw extends E.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;Zv(Lw),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Ki.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||U.postRender(()=>{const l=o.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Ha.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function xm(e){const[t,n]=rp(),r=E.useContext(Ia);return k.jsx(Dw,{...e,layoutGroup:r,switchLayoutGroup:E.useContext(fp),isPresent:t,safeToRemove:n})}const Lw={borderRadius:{...ar,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ar,borderTopRightRadius:ar,borderBottomLeftRadius:ar,borderBottomRightRadius:ar,boxShadow:Mw};function Vw(e,t,n){const r=me(e)?e:Xr(e);return r.start(gu("",r,t,n)),r.animation}function Nw(e){return e instanceof SVGElement&&e.tagName!=="svg"}const jw=(e,t)=>e.depth-t.depth;class _w{constructor(){this.children=[],this.isDirty=!1}add(t){su(this.children,t),this.isDirty=!0}remove(t){ou(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(jw),this.isDirty=!1,this.children.forEach(t)}}function Iw(e,t){const n=rt.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&(It(r),e(s-t))};return U.read(r,!0),()=>It(r)}const km=["TopLeft","TopRight","BottomLeft","BottomRight"],zw=km.length,wf=e=>typeof e=="string"?parseFloat(e):e,Sf=e=>typeof e=="number"||D.test(e);function Fw(e,t,n,r,i,s){i?(e.opacity=K(0,n.opacity!==void 0?n.opacity:1,Ow(r)),e.opacityExit=K(t.opacity!==void 0?t.opacity:1,0,Bw(r))):s&&(e.opacity=K(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<zw;o++){const l=`border${km[o]}Radius`;let a=xf(t,l),u=xf(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Sf(a)===Sf(u)?(e[l]=Math.max(K(wf(a),wf(u),r),0),(nt.test(u)||nt.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=K(t.rotate||0,n.rotate||0,r))}function xf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Ow=Pm(0,.5,Up),Bw=Pm(.5,.95,Me);function Pm(e,t,n){return r=>r<e?0:r>t?1:n(Hn(e,t,r))}function kf(e,t){e.min=t.min,e.max=t.max}function je(e,t){kf(e.x,t.x),kf(e.y,t.y)}function Pf(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Tf(e,t,n,r,i){return e-=t,e=xs(e,1/n,r),i!==void 0&&(e=xs(e,1/i,r)),e}function Uw(e,t=0,n=1,r=.5,i,s=e,o=e){if(nt.test(t)&&(t=parseFloat(t),t=K(o.min,o.max,t/100)-o.min),typeof t!="number")return;let l=K(s.min,s.max,r);e===s&&(l-=t),e.min=Tf(e.min,t,n,l,i),e.max=Tf(e.max,t,n,l,i)}function Cf(e,t,[n,r,i],s,o){Uw(e,t[n],t[r],t[i],t.scale,s,o)}const $w=["x","scaleX","originX"],Ww=["y","scaleY","originY"];function Ef(e,t,n,r){Cf(e.x,t,$w,n?n.x:void 0,r?r.x:void 0),Cf(e.y,t,Ww,n?n.y:void 0,r?r.y:void 0)}function Af(e){return e.translate===0&&e.scale===1}function Tm(e){return Af(e.x)&&Af(e.y)}function Rf(e,t){return e.min===t.min&&e.max===t.max}function Kw(e,t){return Rf(e.x,t.x)&&Rf(e.y,t.y)}function Mf(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Cm(e,t){return Mf(e.x,t.x)&&Mf(e.y,t.y)}function Df(e){return Le(e.x)/Le(e.y)}function Lf(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Hw{constructor(){this.members=[]}add(t){su(this.members,t),t.scheduleRender()}remove(t){if(ou(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Gw(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((i||s||o)&&(r=`translate3d(${i}px, ${s}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:f,rotateY:d,skewX:g,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),f&&(r+=`rotateX(${f}deg) `),d&&(r+=`rotateY(${d}deg) `),g&&(r+=`skewX(${g}deg) `),v&&(r+=`skewY(${v}deg) `)}const l=e.x.scale*t.x,a=e.y.scale*t.y;return(l!==1||a!==1)&&(r+=`scale(${l}, ${a})`),r||"none"}const Yt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},mr=typeof window<"u"&&window.MotionDebug!==void 0,Ao=["","X","Y","Z"],Qw={visibility:"hidden"},Vf=1e3;let Xw=0;function Ro(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Em(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=jp(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",U,!(i||s))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Em(r)}function Am({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},l=t==null?void 0:t()){this.id=Xw++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,mr&&(Yt.totalNodes=Yt.resolvedTargetDeltas=Yt.recalculatedProjection=0),this.nodes.forEach(qw),this.nodes.forEach(nS),this.nodes.forEach(rS),this.nodes.forEach(Jw),mr&&window.MotionDebug.record(Yt)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new _w)}addEventListener(o,l){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new lu),this.eventHandlers.get(o).add(l)}notifyListeners(o,...l){const a=this.eventHandlers.get(o);a&&a.notify(...l)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Nw(o),this.instance=o;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let f;const d=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=Iw(d,250),Ki.hasAnimatedSinceResize&&(Ki.hasAnimatedSinceResize=!1,this.nodes.forEach(jf))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||c.getDefaultTransition()||aS,{onLayoutAnimationStart:S,onLayoutAnimationComplete:h}=c.getProps(),p=!this.targetLayout||!Cm(this.targetLayout,v)||g,m=!d&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||d&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,m);const w={...tu(y,"layout"),onPlay:S,onComplete:h};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else d||jf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,It(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iS),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Em(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Nf);return}this.isUpdating||this.nodes.forEach(eS),this.isUpdating=!1,this.nodes.forEach(tS),this.nodes.forEach(Yw),this.nodes.forEach(Zw),this.clearAllSnapshots();const l=rt.now();le.delta=mt(0,1e3/60,l-le.timestamp),le.timestamp=l,le.isProcessing=!0,vo.update.process(le),vo.preRender.process(le),vo.render.process(le),le.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ha.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(bw),this.sharedNodes.forEach(sS)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,U.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){U.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Z(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(l=!1),l){const a=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:a,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:a}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!Tm(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(l||Xt(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return o&&(a=this.removeTransform(a)),uS(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:l}=this.options;if(!l)return Z();const a=l.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(cS))){const{scroll:c}=this.root;c&&(Mn(a.x,c.offset.x),Mn(a.y,c.offset.y))}return a}removeElementScroll(o){var l;const a=Z();if(je(a,o),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return a;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:f,options:d}=c;c!==this.root&&f&&d.layoutScroll&&(f.wasRoot&&je(a,o),Mn(a.x,f.offset.x),Mn(a.y,f.offset.y))}return a}applyTransform(o,l=!1){const a=Z();je(a,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Dn(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Xt(c.latestValues)&&Dn(a,c.latestValues)}return Xt(this.latestValues)&&Dn(a,this.latestValues),a}removeTransform(o){const l=Z();je(l,o);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!Xt(u.latestValues))continue;zl(u.latestValues)&&u.updateSnapshot();const c=Z(),f=u.measurePageBox();je(c,f),Ef(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Xt(this.latestValues)&&Ef(l,this.latestValues),l}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==le.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=le.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Z(),this.relativeTargetOrigin=Z(),Rr(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),je(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=Z(),this.targetWithTransforms=Z()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),hw(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):je(this.target,this.layout.layoutBox),vm(this.target,this.targetDelta)):je(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Z(),this.relativeTargetOrigin=Z(),Rr(this.relativeTargetOrigin,this.target,g.target),je(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}mr&&Yt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||zl(this.parent.latestValues)||ym(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===le.timestamp&&(u=!1),u)return;const{layout:c,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||f))return;je(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,g=this.treeScale.y;kw(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=Z());const{target:v}=l;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Pf(this.prevProjectionDelta.x,this.projectionDelta.x),Pf(this.prevProjectionDelta.y,this.projectionDelta.y)),Ar(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==d||this.treeScale.y!==g||!Lf(this.projectionDelta.x,this.prevProjectionDelta.x)||!Lf(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),mr&&Yt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Rn(),this.projectionDelta=Rn(),this.projectionDeltaWithTransform=Rn()}setAnimationOrigin(o,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},f=Rn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const d=Z(),g=a?a.source:void 0,v=this.layout?this.layout.source:void 0,y=g!==v,S=this.getStack(),h=!S||S.members.length<=1,p=!!(y&&!h&&this.options.crossfade===!0&&!this.path.some(lS));this.animationProgress=0;let m;this.mixTargetDelta=w=>{const x=w/1e3;_f(f.x,o.x,x),_f(f.y,o.y,x),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Rr(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),oS(this.relativeTarget,this.relativeTargetOrigin,d,x),m&&Kw(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=Z()),je(m,this.relativeTarget)),y&&(this.animationValues=c,Fw(c,u,this.latestValues,x,p,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=x},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(It(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=U.update(()=>{Ki.hasAnimatedSinceResize=!0,this.currentAnimation=Vw(0,Vf,{...o,onUpdate:l=>{this.mixTargetDelta(l),o.onUpdate&&o.onUpdate(l)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Vf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=o;if(!(!l||!a||!u)){if(this!==o&&this.layout&&u&&Rm(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||Z();const f=Le(this.layout.layoutBox.x);a.x.min=o.target.x.min,a.x.max=a.x.min+f;const d=Le(this.layout.layoutBox.y);a.y.min=o.target.y.min,a.y.max=a.y.min+d}je(l,a),Dn(l,c),Ar(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(o,l){this.sharedNodes.has(o)||this.sharedNodes.set(o,new Hw),this.sharedNodes.get(o).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:l}=this.options;return l?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:l}=this.options;return l?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),o&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let l=!1;const{latestValues:a}=o;if((a.z||a.rotate||a.rotateX||a.rotateY||a.rotateZ||a.skewX||a.skewY)&&(l=!0),!l)return;const u={};a.z&&Ro("z",o,u,this.animationValues);for(let c=0;c<Ao.length;c++)Ro(`rotate${Ao[c]}`,o,u,this.animationValues),Ro(`skew${Ao[c]}`,o,u,this.animationValues);o.render();for(const c in u)o.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);o.scheduleRender()}getProjectionStyles(o){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Qw;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=$i(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=$i(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Xt(this.latestValues)&&(y.transform=c?c({},""):"none",this.hasProjected=!1),y}const d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=Gw(this.projectionDeltaWithTransform,this.treeScale,d),c&&(u.transform=c(d,u.transform));const{x:g,y:v}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,f.animationValues?u.opacity=f===this?(a=(l=d.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:u.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const y in gs){if(d[y]===void 0)continue;const{correct:S,applyTo:h}=gs[y],p=u.transform==="none"?d[y]:S(d[y],f);if(h){const m=h.length;for(let w=0;w<m;w++)u[h[w]]=p}else u[y]=p}return this.options.layoutId&&(u.pointerEvents=f===this?$i(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var l;return(l=o.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Nf),this.root.sharedNodes.clear()}}}function Yw(e){e.updateLayout()}function Zw(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?_e(f=>{const d=o?n.measuredBox[f]:n.layoutBox[f],g=Le(d);d.min=r[f].min,d.max=d.min+g}):Rm(s,n.layoutBox,r)&&_e(f=>{const d=o?n.measuredBox[f]:n.layoutBox[f],g=Le(r[f]);d.max=d.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+g)});const l=Rn();Ar(l,r,n.layoutBox);const a=Rn();o?Ar(a,e.applyTransform(i,!0),n.measuredBox):Ar(a,r,n.layoutBox);const u=!Tm(l);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:d,layout:g}=f;if(d&&g){const v=Z();Rr(v,n.layoutBox,d.layoutBox);const y=Z();Rr(y,r,g.layoutBox),Cm(v,y)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=y,e.relativeTargetOrigin=v,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function qw(e){mr&&Yt.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Jw(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function bw(e){e.clearSnapshot()}function Nf(e){e.clearMeasurements()}function eS(e){e.isLayoutDirty=!1}function tS(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function jf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nS(e){e.resolveTargetDelta()}function rS(e){e.calcProjection()}function iS(e){e.resetSkewAndRotation()}function sS(e){e.removeLeadSnapshot()}function _f(e,t,n){e.translate=K(t.translate,0,n),e.scale=K(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function If(e,t,n,r){e.min=K(t.min,n.min,r),e.max=K(t.max,n.max,r)}function oS(e,t,n,r){If(e.x,t.x,n.x,r),If(e.y,t.y,n.y,r)}function lS(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const aS={duration:.45,ease:[.4,0,.1,1]},zf=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Ff=zf("applewebkit/")&&!zf("chrome/")?Math.round:Me;function Of(e){e.min=Ff(e.min),e.max=Ff(e.max)}function uS(e){Of(e.x),Of(e.y)}function Rm(e,t,n){return e==="position"||e==="preserve-aspect"&&!dw(Df(t),Df(n),.2)}function cS(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const fS=Am({attachResizeListener:(e,t)=>Zr(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Mo={current:void 0},Mm=Am({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Mo.current){const e=new fS({});e.mount(window),e.setOptions({layoutScroll:!0}),Mo.current=e}return Mo.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),dS={pan:{Feature:Rw},drag:{Feature:Aw,ProjectionNode:Mm,MeasureLayout:xm}};function Bf(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,s=r[i];s&&U.postRender(()=>s(t,si(t)))}class hS extends Ut{mount(){const{current:t}=this.node;t&&(this.unmount=d0(t,n=>(Bf(this.node,n,"Start"),r=>Bf(this.node,r,"End"))))}unmount(){}}class pS extends Ut{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ii(Zr(this.node.current,"focus",()=>this.onFocus()),Zr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Uf(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),s=r[i];s&&U.postRender(()=>s(t,si(t)))}class mS extends Ut{mount(){const{current:t}=this.node;t&&(this.unmount=g0(t,n=>(Uf(this.node,n,"Start"),(r,{success:i})=>Uf(this.node,r,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ol=new WeakMap,Do=new WeakMap,gS=e=>{const t=Ol.get(e.target);t&&t(e)},yS=e=>{e.forEach(gS)};function vS({root:e,...t}){const n=e||document;Do.has(n)||Do.set(n,{});const r=Do.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(yS,{root:e,...t})),r[i]}function wS(e,t,n){const r=vS(t);return Ol.set(e,n),r.observe(e),()=>{Ol.delete(e),r.unobserve(e)}}const SS={some:0,all:1};class xS extends Ut{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:SS[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),d=u?c:f;d&&d(a)};return wS(this.node.current,o,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(kS(t,n))&&this.startObserver()}unmount(){}}function kS({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const PS={inView:{Feature:xS},tap:{Feature:mS},focus:{Feature:pS},hover:{Feature:hS}},TS={layout:{ProjectionNode:Mm,MeasureLayout:xm}},Bl={current:null},Dm={current:!1};function CS(){if(Dm.current=!0,!!Oa)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Bl.current=e.matches;e.addListener(t),t()}else Bl.current=!1}const ES=[...tm,he,zt],AS=e=>ES.find(em(e)),$f=new WeakMap;function RS(e,t,n){for(const r in t){const i=t[r],s=n[r];if(me(i))e.addValue(r,i);else if(me(s))e.addValue(r,Xr(i,{owner:e}));else if(s!==i)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=e.getStaticValue(r);e.addValue(r,Xr(o!==void 0?o:i,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Wf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class MS{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=hu,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const g=rt.now();this.renderScheduledAt<g&&(this.renderScheduledAt=g,U.render(this.render,!1,!0))};const{latestValues:a,renderState:u,onUpdate:c}=o;this.onUpdate=c,this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=l,this.blockInitialAnimation=!!s,this.isControllingVariants=Us(n),this.isVariantNode=up(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:f,...d}=this.scrapeMotionValuesFromProps(n,{},this);for(const g in d){const v=d[g];a[g]!==void 0&&me(v)&&v.set(a[g],!1)}}mount(t){this.current=t,$f.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Dm.current||CS(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Bl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){$f.delete(this.current),this.projection&&this.projection.unmount(),It(this.notifyUpdate),It(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=fn.has(t),i=n.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&U.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Gn){const n=Gn[t];if(!n)continue;const{isEnabled:r,Feature:i}=n;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){const s=this.features[t];s.isMounted?s.update():(s.mount(),s.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Z()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Wf.length;r++){const i=Wf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s="on"+i,o=t[s];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=RS(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Xr(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(Jp(i)||Wp(i))?i=parseFloat(i):!AS(i)&&zt.test(n)&&(i=Yp(t,n)),this.setBaseTarget(t,me(i)?i.get():i)),me(i)?i.get():i}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let i;if(typeof r=="string"||typeof r=="object"){const o=Qa(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(i=o[t])}if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!me(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new lu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Lm extends MS{constructor(){super(...arguments),this.KeyframeResolver=nm}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;me(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function DS(e){return window.getComputedStyle(e)}class LS extends Lm{constructor(){super(...arguments),this.type="html",this.renderInstance=vp}readValueFromInstance(t,n){if(fn.has(n)){const r=du(n);return r&&r.default||0}else{const r=DS(t),i=(mp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return wm(t,n)}build(t,n,r){Za(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return eu(t,n,r)}}class VS extends Lm{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Z}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(fn.has(n)){const r=du(n);return r&&r.default||0}return n=wp.has(n)?n:Ka(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return kp(t,n,r)}build(t,n,r){qa(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,i){Sp(t,n,r,i)}mount(t){this.isSVGTag=ba(t.tagName),super.mount(t)}}const NS=(e,t)=>Ga(e)?new VS(t):new LS(t,{allowProjection:e!==E.Fragment}),jS=s0({...rw,...PS,...dS,...TS},NS),ae=Sv(jS);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var _S={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IS=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),$e=(e,t)=>{const n=E.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:l="",children:a,...u},c)=>E.createElement("svg",{ref:c,..._S,width:i,height:i,stroke:r,strokeWidth:o?Number(s)*24/Number(i):s,className:["lucide",`lucide-${IS(e)}`,l].join(" "),...u},[...t.map(([f,d])=>E.createElement(f,d)),...Array.isArray(a)?a:[a]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zS=$e("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FS=$e("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OS=$e("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BS=$e("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kf=$e("Keyboard",[["path",{d:"M10 8h.01",key:"1r9ogq"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M14 8h.01",key:"1primd"}],["path",{d:"M16 12h.01",key:"1l6xoz"}],["path",{d:"M18 8h.01",key:"emo2bl"}],["path",{d:"M6 8h.01",key:"x9i8wu"}],["path",{d:"M7 16h10",key:"wp8him"}],["path",{d:"M8 12h.01",key:"czm47f"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const US=$e("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vm=$e("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $S=$e("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WS=$e("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lo=$e("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=$e("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yu=$e("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);class Ln{constructor(t={}){dn(this,"mediaRecorder",null);dn(this,"audioStream",null);dn(this,"audioChunks",[]);dn(this,"startTime",0);dn(this,"options");this.options={sampleRate:16e3,channels:1,bitDepth:16,...t}}async initialize(){try{this.audioStream=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:this.options.sampleRate,channelCount:this.options.channels,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}}),console.log("Microphone access granted")}catch(t){throw console.error("Failed to access microphone:",t),new Error("無法存取麥克風，請檢查權限設定")}}async startRecording(){if(this.audioStream||await this.initialize(),!this.audioStream)throw new Error("音頻流未初始化");this.audioChunks=[];const t=this.getSupportedMimeType();this.mediaRecorder=new MediaRecorder(this.audioStream,{mimeType:t}),this.mediaRecorder.ondataavailable=n=>{n.data.size>0&&this.audioChunks.push(n.data)},this.mediaRecorder.onerror=n=>{console.error("MediaRecorder error:",n)},this.startTime=Date.now(),this.mediaRecorder.start(100),console.log("Recording started")}async stopRecording(){return new Promise((t,n)=>{if(!this.mediaRecorder||this.mediaRecorder.state==="inactive"){n(new Error("錄音器未啟動"));return}this.mediaRecorder.onstop=async()=>{try{const r=Date.now()-this.startTime,i=new Blob(this.audioChunks,{type:this.getSupportedMimeType()}),o={audioBlob:await this.convertToWav(i),duration:r,sampleRate:this.options.sampleRate||16e3};console.log("Recording stopped, duration:",r,"ms"),t(o)}catch(r){n(r)}},this.mediaRecorder.stop()})}cancelRecording(){this.mediaRecorder&&this.mediaRecorder.state!=="inactive"&&this.mediaRecorder.stop(),this.audioChunks=[],console.log("Recording cancelled")}dispose(){this.cancelRecording(),this.audioStream&&(this.audioStream.getTracks().forEach(t=>t.stop()),this.audioStream=null),this.mediaRecorder=null,console.log("AudioRecorder disposed")}isRecording(){var t;return((t=this.mediaRecorder)==null?void 0:t.state)==="recording"}getSupportedMimeType(){const t=["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/wav"];for(const n of t)if(MediaRecorder.isTypeSupported(n))return n;return"audio/webm"}static async blobToArrayBuffer(t){return new Promise((n,r)=>{const i=new FileReader;i.onload=()=>n(i.result),i.onerror=r,i.readAsArrayBuffer(t)})}static async blobToBase64(t){return new Promise((n,r)=>{const i=new FileReader;i.onload=()=>{const o=i.result.split(",")[1];n(o)},i.onerror=r,i.readAsDataURL(t)})}async convertToWav(t){try{const n=new AudioContext,r=await Ln.blobToArrayBuffer(t),i=await n.decodeAudioData(r),s=this.audioBufferToWav(i),o=new Blob([s],{type:"audio/wav"});return n.close(),o}catch(n){return console.error("Error converting to WAV:",n),t}}audioBufferToWav(t){const n=t.length,r=t.numberOfChannels,i=t.sampleRate,o=r*2,l=i*o,a=n*o,u=44+a,c=new ArrayBuffer(u),f=new DataView(c),d=(v,y)=>{for(let S=0;S<y.length;S++)f.setUint8(v+S,y.charCodeAt(S))};d(0,"RIFF"),f.setUint32(4,u-8,!0),d(8,"WAVE"),d(12,"fmt "),f.setUint32(16,16,!0),f.setUint16(20,1,!0),f.setUint16(22,r,!0),f.setUint32(24,i,!0),f.setUint32(28,l,!0),f.setUint16(32,o,!0),f.setUint16(34,16,!0),d(36,"data"),f.setUint32(40,a,!0);let g=44;for(let v=0;v<n;v++)for(let y=0;y<r;y++){const S=Math.max(-1,Math.min(1,t.getChannelData(y)[v]));f.setInt16(g,S*32767,!0),g+=2}return c}}const KS=({mode:e,onClose:t})=>{const[n,r]=E.useState("recording"),[i,s]=E.useState(0),[o,l]=E.useState(""),a=E.useRef(null);E.useEffect(()=>{let d;const g=async()=>{try{a.current=new Ln,await a.current.initialize(),await a.current.startRecording(),d=setInterval(()=>{s(m=>m+1)},1e3),console.log("Recording started successfully")}catch(m){console.error("Failed to start recording:",m),r("error"),l("無法開始錄音，請檢查麥克風權限")}},v=async m=>{(m.key==="Escape"||m.ctrlKey&&m.shiftKey&&(m.key==="C"||m.key==="V"))&&await y()},y=async()=>{if(!(!a.current||!a.current.isRecording()))try{r("processing");const m=await a.current.stopRecording(),w=await Ln.blobToBase64(m.audioBlob);if(window.require){const{ipcRenderer:x}=window.require("electron");x.send("recording-completed",{audioData:w,mode:e,duration:m.duration,sampleRate:m.sampleRate})}console.log("Audio data sent for processing")}catch(m){console.error("Failed to stop recording:",m),r("error"),l("錄音處理失敗，請重試")}},S=(m,w)=>{console.log("Processing complete:",w),r("complete")},h=(m,w)=>{console.log("Processing error:",w),r("error"),l(w.message||"處理失敗，請重試")},p=async()=>{console.log("Received stop recording signal from main process"),await y()};if(g(),window.addEventListener("keydown",v),window.require){const{ipcRenderer:m}=window.require("electron");m.on("processing-complete",S),m.on("processing-error",h),m.on("stop-recording",p)}return()=>{if(d&&clearInterval(d),a.current&&a.current.dispose(),window.removeEventListener("keydown",v),window.require){const{ipcRenderer:m}=window.require("electron");m.removeAllListeners("processing-complete"),m.removeAllListeners("processing-error"),m.removeAllListeners("stop-recording")}}},[e,t]);const u=d=>{const g=Math.floor(d/60),v=d%60;return`${g.toString().padStart(2,"0")}:${v.toString().padStart(2,"0")}`},c=()=>{switch(n){case"recording":return e==="ai"?"正在錄音 - AI 模式":"正在錄音 - 直接模式";case"processing":return e==="ai"?"AI 正在處理...":"語音轉換中...";case"complete":return"完成！";case"error":return"錯誤"}},f=()=>{switch(n){case"recording":return e==="ai"?k.jsx(Vm,{size:32}):k.jsx(Ul,{size:32});case"processing":return k.jsx(US,{size:32,className:"spinning"});case"complete":return k.jsx(Lo,{size:32});case"error":return k.jsx(Lo,{size:32})}};return k.jsx(Ba,{children:k.jsx(ae.div,{className:"recording-overlay",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:k.jsxs(ae.div,{className:`recording-popup ${e}-mode`,initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},transition:{type:"spring",stiffness:300,damping:30},children:[k.jsx("button",{className:"close-button",onClick:()=>{if(window.require){const{ipcRenderer:d}=window.require("electron");d.send("recording-cancelled")}t()},title:"關閉",children:k.jsx(yu,{size:16})}),k.jsxs("div",{className:"recording-content",children:[k.jsx(ae.div,{className:"recording-icon",animate:n==="recording"?{scale:[1,1.1,1]}:{},transition:{repeat:1/0,duration:1.5},children:f()}),k.jsx("h3",{children:c()}),n==="recording"&&k.jsxs("div",{className:"recording-info",children:[k.jsx("div",{className:"duration",children:u(i)}),k.jsx("div",{className:"hint",children:"再次按快捷鍵停止錄音"})]}),n==="processing"&&k.jsx("div",{className:"processing-info",children:k.jsx(ae.div,{className:"progress-bar",initial:{width:0},animate:{width:"100%"},transition:{duration:2}})}),n==="complete"&&k.jsx(ae.div,{className:"complete-info",initial:{opacity:0,y:10},animate:{opacity:1,y:0},children:k.jsx("p",{children:"文字已自動輸入到應用程式"})}),n==="error"&&k.jsxs(ae.div,{className:"error-info",initial:{opacity:0,y:10},animate:{opacity:1,y:0},children:[k.jsx("p",{children:o}),k.jsx("button",{className:"retry-btn",onClick:async()=>{r("recording"),l(""),s(0);try{a.current&&a.current.dispose(),a.current=new Ln,await a.current.initialize(),await a.current.startRecording();const d=setInterval(()=>{s(g=>g+1)},1e3);console.log("Recording restarted successfully")}catch(d){console.error("Failed to restart recording:",d),r("error"),l("重新開始錄音失敗，請檢查麥克風權限")}},children:"重試"})]})]}),n==="recording"&&k.jsxs(ae.button,{className:"stop-btn",onClick:async()=>{if(a.current&&a.current.isRecording())try{r("processing");const d=await a.current.stopRecording(),g=await Ln.blobToBase64(d.audioBlob);if(window.require){const{ipcRenderer:v}=window.require("electron");v.send("recording-completed",{audioData:g,mode:e,duration:d.duration,sampleRate:d.sampleRate})}}catch(d){console.error("Failed to stop recording:",d),r("error"),l("停止錄音失敗，請重試")}},whileHover:{scale:1.05},whileTap:{scale:.95},children:[k.jsx(Lo,{size:16}),"停止錄音"]})]})})})},HS=({hotkeys:e,onSave:t,onClose:n})=>{const[r,i]=E.useState(e),[s,o]=E.useState(null),[l,a]=E.useState({azureSpeechKey:"",azureSpeechRegion:"eastus",azureOpenAIKey:"",azureOpenAIEndpoint:""}),[u,c]=E.useState(!1),f=v=>{o(v);const y=S=>{S.preventDefault();const h=[];if(S.ctrlKey&&h.push("Ctrl"),S.shiftKey&&h.push("Shift"),S.altKey&&h.push("Alt"),S.metaKey&&h.push("Meta"),S.key!=="Control"&&S.key!=="Shift"&&S.key!=="Alt"&&S.key!=="Meta"&&h.push(S.key.toUpperCase()),h.length>1){const p=h.join("+");i(m=>({...m,[v]:p})),o(null),document.removeEventListener("keydown",y)}};document.addEventListener("keydown",y),setTimeout(()=>{o(null),document.removeEventListener("keydown",y)},5e3)},d=()=>{t({hotkeys:r,apiKeys:l}),n()},g=()=>{i(e),n()};return k.jsx(Ba,{children:k.jsx(ae.div,{className:"settings-overlay",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:k.jsxs(ae.div,{className:"settings-panel",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},transition:{type:"spring",stiffness:300,damping:30},children:[k.jsxs("div",{className:"settings-header",children:[k.jsx("h2",{children:"設定"}),k.jsx("button",{className:"close-btn",onClick:g,children:k.jsx(yu,{size:20})})]}),k.jsxs("div",{className:"settings-content",children:[k.jsxs("div",{className:"setting-group",children:[k.jsx("h3",{children:"快捷鍵設定"}),k.jsxs("div",{className:"hotkey-setting",children:[k.jsx("label",{children:"AI 語音助手模式"}),k.jsxs("div",{className:"hotkey-input",children:[k.jsx("input",{type:"text",value:r.aiMode,readOnly:!0,placeholder:"點擊錄製按鈕設定快捷鍵"}),k.jsxs("button",{className:`record-btn ${s==="aiMode"?"recording":""}`,onClick:()=>f("aiMode"),disabled:s!==null,children:[k.jsx(Kf,{size:16}),s==="aiMode"?"按下快捷鍵...":"錄製"]})]})]}),k.jsxs("div",{className:"hotkey-setting",children:[k.jsx("label",{children:"直接語音轉文字模式"}),k.jsxs("div",{className:"hotkey-input",children:[k.jsx("input",{type:"text",value:r.directMode,readOnly:!0,placeholder:"點擊錄製按鈕設定快捷鍵"}),k.jsxs("button",{className:`record-btn ${s==="directMode"?"recording":""}`,onClick:()=>f("directMode"),disabled:s!==null,children:[k.jsx(Kf,{size:16}),s==="directMode"?"按下快捷鍵...":"錄製"]})]})]})]}),k.jsxs("div",{className:"setting-group",children:[k.jsx("h3",{children:"API 配置"}),k.jsx("div",{className:"api-toggle",children:k.jsxs("button",{className:"toggle-btn",onClick:()=>c(!u),children:[u?"隱藏":"顯示"," API 金鑰設定"]})}),u&&k.jsxs("div",{className:"api-settings",children:[k.jsxs("div",{className:"api-setting",children:[k.jsx("label",{children:"Azure Speech Service API Key"}),k.jsx("input",{type:"password",value:l.azureSpeechKey,onChange:v=>a(y=>({...y,azureSpeechKey:v.target.value})),placeholder:"輸入 Azure Speech Service API Key"})]}),k.jsxs("div",{className:"api-setting",children:[k.jsx("label",{children:"Azure Speech Service Region"}),k.jsx("input",{type:"text",value:l.azureSpeechRegion,onChange:v=>a(y=>({...y,azureSpeechRegion:v.target.value})),placeholder:"例如: eastus"})]}),k.jsxs("div",{className:"api-setting",children:[k.jsx("label",{children:"Azure OpenAI API Key"}),k.jsx("input",{type:"password",value:l.azureOpenAIKey,onChange:v=>a(y=>({...y,azureOpenAIKey:v.target.value})),placeholder:"輸入 Azure OpenAI API Key"})]}),k.jsxs("div",{className:"api-setting",children:[k.jsx("label",{children:"Azure OpenAI Endpoint"}),k.jsx("input",{type:"text",value:l.azureOpenAIEndpoint,onChange:v=>a(y=>({...y,azureOpenAIEndpoint:v.target.value})),placeholder:"例如: https://your-resource.openai.azure.com/openai/deployments/gpt-4o-mini-audio-preview/chat/completions"})]})]})]}),k.jsxs("div",{className:"setting-group",children:[k.jsx("h3",{children:"使用說明"}),k.jsxs("div",{className:"instructions",children:[k.jsx("p",{children:"• 按下 AI 模式快捷鍵開始錄音，說出指令讓 AI 理解並生成回應"}),k.jsx("p",{children:"• 按下直接模式快捷鍵開始錄音，語音會直接轉換為文字"}),k.jsx("p",{children:"• 錄音期間再次按下相同快捷鍵或 ESC 鍵停止錄音"}),k.jsx("p",{children:"• 處理完成後文字會自動輸入到當前焦點的應用程式"}),k.jsx("p",{children:"• 首次使用請先在 API 配置中設定您的 Azure 服務金鑰"})]})]})]}),k.jsxs("div",{className:"settings-footer",children:[k.jsx("button",{className:"cancel-btn",onClick:g,children:"取消"}),k.jsxs("button",{className:"save-btn",onClick:d,children:[k.jsx($S,{size:16}),"儲存設定"]})]})]})})})},GS=({notifications:e,onRemove:t})=>{const n=i=>{switch(i){case"success":return k.jsx(OS,{size:20});case"error":return k.jsx(zS,{size:20});case"warning":return k.jsx(FS,{size:20});case"info":default:return k.jsx(BS,{size:20})}},r=i=>{switch(i){case"success":return"toast-success";case"error":return"toast-error";case"warning":return"toast-warning";case"info":default:return"toast-info"}};return k.jsx("div",{className:"notification-container",children:k.jsx(Ba,{children:e.map(i=>k.jsx(QS,{notification:i,onRemove:t,getIcon:n,getTypeClass:r},i.id))})})},QS=({notification:e,onRemove:t,getIcon:n,getTypeClass:r})=>(E.useEffect(()=>{const i=e.duration||3e3,s=setTimeout(()=>{t(e.id)},i);return()=>clearTimeout(s)},[e.id,e.duration,t]),k.jsxs(ae.div,{className:`notification-toast ${r(e.type)}`,initial:{opacity:0,x:300,scale:.8},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:300,scale:.8},transition:{type:"spring",stiffness:300,damping:30},layout:!0,children:[k.jsxs("div",{className:"toast-content",children:[k.jsx("div",{className:"toast-icon",children:n(e.type)}),k.jsx("div",{className:"toast-message",children:e.message}),k.jsx("button",{className:"toast-close",onClick:()=>t(e.id),children:k.jsx(yu,{size:16})})]}),k.jsx(ae.div,{className:"toast-progress",initial:{width:"100%"},animate:{width:"0%"},transition:{duration:(e.duration||3e3)/1e3,ease:"linear"}})]}));function XS(){const[e,t]=E.useState(!1),[n,r]=E.useState(null),[i,s]=E.useState(!1),[o,l]=E.useState({aiMode:"Ctrl+Shift+C",directMode:"Ctrl+Shift+V"}),[a,u]=E.useState([]),c=(g,v="info",y)=>{const h={id:Date.now().toString(),message:g,type:v,duration:y};u(p=>[...p,h])},f=g=>{u(v=>v.filter(y=>y.id!==g))};E.useEffect(()=>{const g=(y,S,h)=>{switch(console.log("Received IPC message:",S,h),S){case"start-recording":console.log("Starting recording with mode:",h.mode),t(!0),r(h.mode);break;case"stop-recording":t(!1),r(null);break;case"show-settings":s(!0);break;case"notification":c(h.message,h.type,h.duration);break;case"error":c(h.message,"error",5e3);break}},v=y=>{const{type:S,data:h}=y.data;g(null,S,h)};try{const{ipcRenderer:y}=require("electron");y.on("start-recording",(S,h)=>{g(S,"start-recording",h)}),y.on("stop-recording",S=>{g(S,"stop-recording")}),y.on("show-settings",S=>{g(S,"show-settings")}),y.on("notification",(S,h)=>{g(S,"notification",h)}),y.on("error",(S,h)=>{g(S,"error",h)}),console.log("IPC listeners registered")}catch{console.log("Electron not available, using window.postMessage")}return window.addEventListener("message",v),()=>{window.removeEventListener("message",v);try{const{ipcRenderer:y}=require("electron");y.removeAllListeners("start-recording"),y.removeAllListeners("stop-recording"),y.removeAllListeners("show-settings"),y.removeAllListeners("notification"),y.removeAllListeners("error")}catch{}}},[]);const d=g=>{if(l(g.hotkeys),window.require){const{ipcRenderer:v}=window.require("electron");v.send("update-hotkeys",g.hotkeys),v.send("update-api-config",g.apiKeys)}else window.postMessage({type:"update-hotkeys",data:g.hotkeys},"*"),window.postMessage({type:"update-api-config",data:g.apiKeys},"*")};return k.jsxs("div",{className:"app",children:[k.jsxs(ae.div,{className:"main-container",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},children:[k.jsxs("div",{className:"header",children:[k.jsxs(ae.div,{className:"logo",whileHover:{scale:1.05},whileTap:{scale:.95},children:[k.jsx(Ul,{size:32}),k.jsx("h1",{children:"SpeechPilot"})]}),k.jsx(ae.button,{className:"settings-btn",onClick:()=>s(!0),whileHover:{scale:1.1},whileTap:{scale:.9},children:k.jsx(WS,{size:20})})]}),k.jsxs("div",{className:"content",children:[k.jsxs("div",{className:"mode-cards",children:[k.jsxs(ae.div,{className:"mode-card ai-mode",whileHover:{scale:1.02,y:-5},transition:{type:"spring",stiffness:300},onClick:()=>{if(window.require){const{ipcRenderer:g}=window.require("electron");g.send("start-recording-request",{mode:"ai"})}},children:[k.jsx(Vm,{size:48}),k.jsx("h3",{children:"AI 語音助手"}),k.jsx("p",{children:"說出指令，AI 理解並生成回應"}),k.jsx("div",{className:"hotkey",children:o.aiMode})]}),k.jsxs(ae.div,{className:"mode-card direct-mode",whileHover:{scale:1.02,y:-5},transition:{type:"spring",stiffness:300},onClick:()=>{if(window.require){const{ipcRenderer:g}=window.require("electron");g.send("start-recording-request",{mode:"direct"})}},children:[k.jsx(Ul,{size:48}),k.jsx("h3",{children:"直接語音轉文字"}),k.jsx("p",{children:"語音直接轉換為文字輸入"}),k.jsx("div",{className:"hotkey",children:o.directMode})]})]}),k.jsx(ae.div,{className:"status",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:k.jsx("p",{children:"按下快捷鍵開始使用語音功能"})})]})]}),e&&k.jsx(KS,{mode:n,onClose:()=>t(!1)}),i&&k.jsx(HS,{hotkeys:o,onSave:d,onClose:()=>s(!1)}),k.jsx(GS,{notifications:a,onRemove:f})]})}Vo.createRoot(document.getElementById("root")).render(k.jsx(qm.StrictMode,{children:k.jsx(XS,{})}));
