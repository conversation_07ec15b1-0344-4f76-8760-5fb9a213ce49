/**
 * 配置管理器
 * 處理環境變數和應用程式配置
 */

import path from 'path'
import fs from 'fs'

export interface AzureSpeechConfig {
  subscriptionKey: string
  region: string
  language: string
}

export interface AzureOpenAIConfig {
  apiKey: string
  endpoint: string
  model: string
}

export interface AppConfig {
  isDev: boolean
  logLevel: string
}

export interface Config {
  azureSpeech: AzureSpeechConfig
  azureOpenAI: AzureOpenAIConfig
  app: AppConfig
}

export class ConfigManager {
  private config: Config

  constructor() {
    this.config = this.getDefaultConfig()
    this.loadConfig()
  }

  /**
   * 獲取預設配置
   */
  private getDefaultConfig(): Config {
    return {
      azureSpeech: {
        subscriptionKey: '',
        region: 'eastus',
        language: 'zh-TW'
      },
      azureOpenAI: {
        apiKey: '',
        endpoint: '',
        model: 'gpt-4o-mini-audio-preview'
      },
      app: {
        isDev: process.env.NODE_ENV === 'development',
        logLevel: 'info'
      }
    }
  }

  /**
   * 載入配置
   */
  loadConfig() {
    try {
      // 嘗試載入 .env 檔案
      this.loadEnvFile()
      
      // 從環境變數載入配置
      this.config = {
        // Azure Speech Service 配置
        azureSpeech: {
          subscriptionKey: process.env.AZURE_SPEECH_SERVICE_API_KEY || '',
          region: process.env.AZURE_SPEECH_SERVICE_REGION || 'eastus',
          language: 'zh-TW'
        },
        
        // Azure OpenAI 配置
        azureOpenAI: {
          apiKey: process.env.AZURE_OPENAI_API_KEY || '',
          endpoint: process.env.AZURE_OPENAI_ENDPOINT || '',
          model: process.env.AZURE_OPENAI_MODEL || 'gpt-4o-mini-audio-preview'
        },
        
        // 應用程式配置
        app: {
          isDev: process.env.NODE_ENV === 'development',
          logLevel: process.env.LOG_LEVEL || 'info'
        }
      }
      
      console.log('Configuration loaded successfully')
    } catch (error) {
      console.error('Failed to load configuration:', error)
    }
  }

  /**
   * 載入 .env 檔案
   */
  private loadEnvFile() {
    const envPath = path.join(process.cwd(), '.env.local')
    console.log('Loading environment from:', envPath)
    
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8')
      const lines = envContent.split('\n')
      
      lines.forEach(line => {
        const trimmedLine = line.trim()
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          const [key, ...valueParts] = trimmedLine.split('=')
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').replace(/^["']|["']$/g, '')
            process.env[key.trim()] = value
          }
        }
      })
    }
  }



  /**
   * 獲取完整配置
   */
  getConfig(): Config {
    return { ...this.config }
  }

  /**
   * 獲取 Azure Speech Service 配置
   */
  getAzureSpeechConfig(): AzureSpeechConfig {
    return { ...this.config.azureSpeech }
  }

  /**
   * 獲取 Azure OpenAI 配置
   */
  getAzureOpenAIConfig(): AzureOpenAIConfig {
    return { ...this.config.azureOpenAI }
  }

  /**
   * 獲取應用程式配置
   */
  getAppConfig(): AppConfig {
    return { ...this.config.app }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<Config>) {
    this.config = {
      ...this.config,
      ...newConfig
    }
  }

  /**
   * 驗證 Azure Speech Service 配置
   */
  validateAzureSpeechConfig(): boolean {
    const config = this.config.azureSpeech
    return !!(config.subscriptionKey && config.region)
  }

  /**
   * 驗證 Azure OpenAI 配置
   */
  validateAzureOpenAIConfig(): boolean {
    const config = this.config.azureOpenAI
    return !!(config.apiKey && config.endpoint)
  }

  /**
   * 獲取配置狀態
   */
  getConfigStatus() {
    return {
      azureSpeech: this.validateAzureSpeechConfig(),
      azureOpenAI: this.validateAzureOpenAIConfig()
    }
  }
}
