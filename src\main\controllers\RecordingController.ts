/**
 * 錄音控制器
 * 負責管理錄音流程和狀態
 */

import { WindowManager } from '../managers/WindowManager'

export type RecordingMode = 'ai' | 'direct'

export class RecordingController {
  private windowManager: WindowManager
  private isRecording = false
  private currentMode: RecordingMode | null = null

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager
  }

  /**
   * 處理語音輸入請求
   */
  handleVoiceInput(mode: RecordingMode) {
    if (this.isRecording) {
      // 如果正在錄音，則停止錄音
      this.stopRecording()
    } else {
      // 如果沒有錄音，則開始錄音
      this.startRecording(mode)
    }
  }

  /**
   * 開始錄音
   */
  startRecording(mode: RecordingMode) {
    console.log(`Starting recording in ${mode} mode`)
    
    this.isRecording = true
    this.currentMode = mode

    // 顯示錄音視窗
    const recordingWindow = this.windowManager.showRecordingWindow()

    // 確保錄音視窗內容載入完成後再發送訊息
    if (recordingWindow.webContents.isLoading()) {
      recordingWindow.webContents.once('did-finish-load', () => {
        this.windowManager.sendToRecordingWindow('start-recording', { mode })
        console.log(`Recording started in ${mode} mode`)
      })
    } else {
      this.windowManager.sendToRecordingWindow('start-recording', { mode })
      console.log(`Recording started in ${mode} mode`)
    }
  }

  /**
   * 停止錄音
   */
  stopRecording() {
    if (!this.isRecording) return

    console.log('Stopping recording, waiting for audio processing...')

    // 發送停止錄音訊息到錄音視窗
    this.windowManager.sendToRecordingWindow('stop-recording')
  }

  /**
   * 重試錄音（用於錯誤後重新開始）
   */
  retryRecording() {
    if (this.currentMode) {
      console.log(`Retrying recording in ${this.currentMode} mode`)
      
      // 重置狀態
      this.isRecording = false
      
      // 重新開始錄音
      this.startRecording(this.currentMode)
    }
  }

  /**
   * 取消錄音
   */
  cancelRecording() {
    console.log('Recording cancelled by user')
    
    this.isRecording = false
    this.currentMode = null
    this.windowManager.closeRecordingWindow()
  }

  /**
   * 錄音完成處理
   */
  onRecordingCompleted() {
    // 注意：不要在這裡立即重置狀態
    // 等待音頻處理完成後再重置
    console.log('Recording completed, waiting for processing...')
  }

  /**
   * 處理完成後重置狀態
   */
  onProcessingCompleted() {
    console.log('Audio processing completed')
    
    this.isRecording = false
    this.currentMode = null
    
    // 延遲關閉錄音視窗，讓用戶看到完成訊息
    setTimeout(() => {
      this.windowManager.closeRecordingWindow()
    }, 1500)
  }

  /**
   * 處理錯誤後重置狀態
   */
  onProcessingError() {
    console.log('Audio processing failed')
    
    this.isRecording = false
    // 保留 currentMode 以便重試
  }

  /**
   * 獲取當前錄音狀態
   */
  getRecordingState() {
    return {
      isRecording: this.isRecording,
      currentMode: this.currentMode
    }
  }

  /**
   * 檢查是否正在錄音
   */
  isCurrentlyRecording(): boolean {
    return this.isRecording
  }

  /**
   * 獲取當前模式
   */
  getCurrentMode(): RecordingMode | null {
    return this.currentMode
  }
}
