"use strict";
/**
 * Electron 主程序入口點
 * 負責應用程式初始化和基本設定
 */
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const WindowManager_1 = require("./managers/WindowManager");
const TrayManager_1 = require("./managers/TrayManager");
const ShortcutManager_1 = require("./managers/ShortcutManager");
const IPCManager_1 = require("./managers/IPCManager");
const ServiceManager_1 = require("./managers/ServiceManager");
class SpeechPilotApp {
    constructor() {
        this.serviceManager = new ServiceManager_1.ServiceManager();
        this.windowManager = new WindowManager_1.WindowManager();
        this.trayManager = new TrayManager_1.TrayManager(this.windowManager);
        this.shortcutManager = new ShortcutManager_1.ShortcutManager(this.windowManager);
        this.ipcManager = new IPCManager_1.IPCManager(this.windowManager, this.serviceManager);
    }
    async initialize() {
        try {
            console.log('Initializing SpeechPilot application...');
            // 初始化服務
            await this.serviceManager.initialize();
            // 創建主視窗（但不顯示）
            this.windowManager.createMainWindow();
            // 創建托盤
            this.trayManager.createTray();
            // 註冊全域快捷鍵
            this.shortcutManager.registerShortcuts();
            // 設定 IPC 事件處理
            this.ipcManager.setupEventHandlers();
            // 設定管理器之間的連接
            this.ipcManager.setRecordingController(this.shortcutManager.getRecordingController());
            this.ipcManager.setShortcutManager(this.shortcutManager);
            console.log('SpeechPilot application initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize application:', error);
            throw error;
        }
    }
    async shutdown() {
        console.log('Shutting down SpeechPilot application...');
        this.shortcutManager.unregisterAll();
        this.trayManager.destroy();
        await this.serviceManager.shutdown();
        console.log('Application shutdown complete');
    }
}
// 應用程式實例
let speechPilotApp = null;
// 應用程式就緒時初始化
electron_1.app.whenReady().then(async () => {
    try {
        speechPilotApp = new SpeechPilotApp();
        await speechPilotApp.initialize();
    }
    catch (error) {
        console.error('Failed to start application:', error);
        electron_1.app.quit();
    }
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            speechPilotApp?.windowManager.createMainWindow();
        }
    });
});
// 所有視窗關閉時的處理
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
// 應用程式即將退出時的清理
electron_1.app.on('will-quit', async () => {
    if (speechPilotApp) {
        await speechPilotApp.shutdown();
    }
});
electron_1.app.on('before-quit', () => {
    electron_1.app.isQuiting = true;
});
//# sourceMappingURL=index.js.map