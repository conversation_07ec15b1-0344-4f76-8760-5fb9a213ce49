/**
 * 錯誤處理服務
 * 統一處理應用程式中的錯誤和用戶反饋
 */

export interface ErrorInfo {
  message: string
  type: string
  context: string
  timestamp: number
  userMessage: string
  stack?: string
}

export type ErrorCallback = (errorInfo: ErrorInfo) => void
export type NotificationCallback = (message: string, type: string) => void

export class ErrorHandler {
  private errorCallbacks: ErrorCallback[] = []
  private notificationCallbacks: NotificationCallback[] = []

  constructor() {
    console.log('Error Handler initialized')
  }

  /**
   * 註冊錯誤回調函數
   */
  onError(callback: ErrorCallback) {
    this.errorCallbacks.push(callback)
  }

  /**
   * 註冊通知回調函數
   */
  onNotification(callback: NotificationCallback) {
    this.notificationCallbacks.push(callback)
  }

  /**
   * 處理錯誤
   */
  handleError(error: any, context = ''): ErrorInfo {
    const errorInfo = this.parseError(error, context)
    
    console.error(`[${context}] Error:`, errorInfo)
    
    // 通知所有註冊的錯誤處理器
    this.errorCallbacks.forEach(callback => {
      try {
        callback(errorInfo)
      } catch (callbackError) {
        console.error('Error in error callback:', callbackError)
      }
    })
    
    return errorInfo
  }

  /**
   * 處理語音相關錯誤
   */
  handleSpeechError(error: any): ErrorInfo {
    return this.handleError(error, 'Speech')
  }

  /**
   * 處理 OpenAI 相關錯誤
   */
  handleOpenAIError(error: any): ErrorInfo {
    return this.handleError(error, 'OpenAI')
  }

  /**
   * 處理文字輸入相關錯誤
   */
  handleTextInputError(error: any): ErrorInfo {
    return this.handleError(error, 'TextInput')
  }

  /**
   * 解析錯誤信息
   */
  private parseError(error: any, context = ''): ErrorInfo {
    let errorInfo: ErrorInfo = {
      message: '未知錯誤',
      type: 'unknown',
      context,
      timestamp: Date.now(),
      userMessage: '發生未知錯誤，請重試'
    }

    if (typeof error === 'string') {
      errorInfo.message = error
      errorInfo.userMessage = error
      errorInfo.type = 'custom'
    } else if (error instanceof Error) {
      errorInfo.message = error.message
      errorInfo.stack = error.stack
      errorInfo.type = this.categorizeError(error)
      errorInfo.userMessage = this.getUserFriendlyMessage(error)
    } else if (error && typeof error === 'object') {
      errorInfo.message = error.message || JSON.stringify(error)
      errorInfo.userMessage = error.userMessage || this.getUserFriendlyMessage(error)
      errorInfo.type = error.type || 'object'
    }

    return errorInfo
  }

  /**
   * 分類錯誤類型
   */
  private categorizeError(error: Error): string {
    const message = error.message.toLowerCase()
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network'
    } else if (message.includes('permission') || message.includes('access')) {
      return 'permission'
    } else if (message.includes('api') || message.includes('unauthorized')) {
      return 'api'
    } else if (message.includes('audio') || message.includes('microphone')) {
      return 'audio'
    } else if (message.includes('timeout')) {
      return 'timeout'
    } else {
      return 'general'
    }
  }

  /**
   * 獲取用戶友好的錯誤訊息
   */
  private getUserFriendlyMessage(error: any): string {
    const message = (error.message || '').toLowerCase()
    
    // 網路相關錯誤
    if (message.includes('network') || message.includes('fetch failed')) {
      return '網路連接失敗，請檢查網路設定'
    } else if (message.includes('timeout')) {
      return '請求超時，請重試'
    }
    
    // 權限相關錯誤
    else if (message.includes('permission') || message.includes('access')) {
      return '權限不足，請檢查應用程式權限設定'
    } else if (message.includes('microphone')) {
      return '無法存取麥克風，請檢查麥克風權限'
    }
    
    // API 相關錯誤
    else if (message.includes('api key') || message.includes('unauthorized')) {
      return 'API 金鑰無效，請檢查設定'
    } else if (message.includes('quota') || message.includes('limit')) {
      return 'API 使用額度已達上限，請稍後重試'
    }
    
    // 音頻相關錯誤
    else if (message.includes('audio') || message.includes('recording')) {
      return '音頻處理失敗，請重試'
    }
    
    // 預設訊息
    else {
      return '發生錯誤，請重試'
    }
  }

  /**
   * 發送通知
   */
  private notify(message: string, type: string) {
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(message, type)
      } catch (callbackError) {
        console.error('Error in notification callback:', callbackError)
      }
    })
  }

  /**
   * 發送成功通知
   */
  success(message: string) {
    console.log(`[SUCCESS] ${message}`)
    this.notify(message, 'success')
  }

  /**
   * 發送資訊通知
   */
  info(message: string) {
    console.log(`[INFO] ${message}`)
    this.notify(message, 'info')
  }

  /**
   * 發送警告通知
   */
  warning(message: string) {
    console.warn(`[WARNING] ${message}`)
    this.notify(message, 'warning')
  }

  /**
   * 發送錯誤通知
   */
  error(message: string) {
    console.error(`[ERROR] ${message}`)
    this.notify(message, 'error')
  }
}
