/**
 * 快捷鍵管理器
 * 負責註冊和管理全域快捷鍵
 */

import { globalShortcut } from 'electron'
import { WindowManager } from './WindowManager'
import { RecordingController } from '../controllers/RecordingController'

export interface HotkeyConfig {
  aiMode: string
  directMode: string
}

export class ShortcutManager {
  private windowManager: WindowManager
  private recordingController: RecordingController
  private hotkeys: HotkeyConfig = {
    aiMode: 'CommandOrControl+Shift+C',
    directMode: 'CommandOrControl+Shift+V'
  }

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager
    this.recordingController = new RecordingController(windowManager)
  }

  /**
   * 註冊全域快捷鍵
   */
  registerShortcuts() {
    this.unregisterAll() // 先清除現有的快捷鍵

    try {
      // AI 模式快捷鍵
      const aiModeRegistered = globalShortcut.register(this.hotkeys.aiMode, () => {
        console.log('AI mode hotkey triggered')
        this.recordingController.handleVoiceInput('ai')
      })

      // 直接模式快捷鍵
      const directModeRegistered = globalShortcut.register(this.hotkeys.directMode, () => {
        console.log('Direct mode hotkey triggered')
        this.recordingController.handleVoiceInput('direct')
      })

      if (aiModeRegistered && directModeRegistered) {
        console.log('Global shortcuts registered:', this.hotkeys)
      } else {
        console.warn('Some shortcuts failed to register')
      }
    } catch (error) {
      console.error('Failed to register shortcuts:', error)
    }
  }

  /**
   * 更新快捷鍵配置
   */
  updateHotkeys(newHotkeys: HotkeyConfig) {
    this.hotkeys = { ...newHotkeys }
    this.registerShortcuts()
  }

  /**
   * 取消註冊所有快捷鍵
   */
  unregisterAll() {
    globalShortcut.unregisterAll()
  }

  /**
   * 獲取當前快捷鍵配置
   */
  getHotkeys(): HotkeyConfig {
    return { ...this.hotkeys }
  }

  /**
   * 獲取錄音控制器
   */
  getRecordingController(): RecordingController {
    return this.recordingController
  }
}
