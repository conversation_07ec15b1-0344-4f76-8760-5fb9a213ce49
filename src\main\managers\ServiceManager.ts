/**
 * 服務管理器
 * 負責初始化和管理所有服務
 */

import { ConfigManager } from '../../services/ConfigManager'
import { AzureSpeechService } from '../../services/AzureSpeechService'
import { AzureOpenAIService } from '../../services/AzureOpenAIService'
import { TextInputService } from '../../services/TextInputService'
import { ErrorHandler } from '../../services/ErrorHandler'

export class ServiceManager {
  private configManager: ConfigManager
  private speechService: AzureSpeechService | null = null
  private openAIService: AzureOpenAIService | null = null
  private textInputService: TextInputService | null = null
  private errorHandler: ErrorHandler

  constructor() {
    this.configManager = new ConfigManager()
    this.errorHandler = new ErrorHandler()
  }

  /**
   * 初始化所有服務
   */
  async initialize() {
    console.log('Initializing services...')

    try {
      // 初始化語音服務
      await this.initializeSpeechService()
      
      // 初始化 OpenAI 服務
      await this.initializeOpenAIService()
      
      // 初始化文字輸入服務
      await this.initializeTextInputService()

      console.log('All services initialized successfully')
    } catch (error) {
      console.error('Failed to initialize services:', error)
      throw error
    }
  }

  /**
   * 初始化語音服務
   */
  private async initializeSpeechService() {
    try {
      const config = this.configManager.getAzureSpeechConfig()

      if (config.subscriptionKey && config.region) {
        this.speechService = new AzureSpeechService({
          subscriptionKey: config.subscriptionKey,
          region: config.region,
          language: config.language
        })
        console.log('Azure Speech Service initialized successfully')
        console.log('Azure Speech Service 初始化成功')
      } else {
        console.warn('Azure Speech Service credentials not found, service not initialized')
      }
    } catch (error) {
      console.error('Failed to initialize Azure Speech Service:', error)
      this.errorHandler.handleSpeechError(error)
    }
  }

  /**
   * 初始化 OpenAI 服務
   */
  private async initializeOpenAIService() {
    try {
      const config = this.configManager.getAzureOpenAIConfig()

      if (config.apiKey && config.endpoint) {
        this.openAIService = new AzureOpenAIService({
          apiKey: config.apiKey,
          endpoint: config.endpoint,
          model: config.model
        })
        console.log('Azure OpenAI Service initialized successfully')
        console.log('Azure OpenAI Service 初始化成功')
      } else {
        console.warn('Azure OpenAI Service credentials not found, service not initialized')
      }
    } catch (error) {
      console.error('Failed to initialize Azure OpenAI Service:', error)
      this.errorHandler.handleOpenAIError(error)
    }
  }

  /**
   * 初始化文字輸入服務
   */
  private async initializeTextInputService() {
    try {
      this.textInputService = new TextInputService()
      console.log('Text Input Service initialized with nut.js')
      console.log('Text Input Service 初始化成功')
    } catch (error) {
      console.error('Failed to initialize Text Input Service:', error)
      this.errorHandler.handleTextInputError(error)
    }
  }

  /**
   * 獲取語音服務
   */
  getSpeechService(): AzureSpeechService | null {
    return this.speechService
  }

  /**
   * 獲取 OpenAI 服務
   */
  getOpenAIService(): AzureOpenAIService | null {
    return this.openAIService
  }

  /**
   * 獲取文字輸入服務
   */
  getTextInputService(): TextInputService | null {
    return this.textInputService
  }

  /**
   * 獲取錯誤處理器
   */
  getErrorHandler(): ErrorHandler {
    return this.errorHandler
  }

  /**
   * 獲取配置管理器
   */
  getConfigManager(): ConfigManager {
    return this.configManager
  }

  /**
   * 更新 API 配置
   */
  async updateApiConfig(apiConfig: any) {
    try {
      console.log('Updating API configuration')
      
      // 更新配置
      this.configManager.updateConfig(apiConfig)
      
      // 重新初始化相關服務
      if (apiConfig.azureSpeech) {
        await this.initializeSpeechService()
      }

      if (apiConfig.azureOpenAI) {
        await this.initializeOpenAIService()
      }
      
      console.log('API configuration updated successfully')
    } catch (error) {
      console.error('Failed to update API configuration:', error)
      throw error
    }
  }

  /**
   * 關閉所有服務
   */
  async shutdown() {
    console.log('Shutting down services...')
    
    // 這裡可以添加服務清理邏輯
    this.speechService = null
    this.openAIService = null
    this.textInputService = null
    
    console.log('Services shutdown complete')
  }
}
