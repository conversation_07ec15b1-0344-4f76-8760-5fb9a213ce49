{"version": 3, "file": "ErrorHandler.js", "sourceRoot": "", "sources": ["../../src/services/ErrorHandler.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAcH,MAAa,YAAY;IAIvB;QAHQ,mBAAc,GAAoB,EAAE,CAAA;QACpC,0BAAqB,GAA2B,EAAE,CAAA;QAGxD,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,QAAuB;QAC7B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACpC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAA8B;QAC3C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC3C,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,KAAU,EAAE,OAAO,GAAG,EAAE;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;QAEjD,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,UAAU,EAAE,SAAS,CAAC,CAAA;QAE/C,eAAe;QACf,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACrC,IAAI,CAAC;gBACH,QAAQ,CAAC,SAAS,CAAC,CAAA;YACrB,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,aAAa,CAAC,CAAA;YAC1D,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,SAAS,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,KAAU;QAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,KAAU;QAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,KAAU,EAAE,OAAO,GAAG,EAAE;QACzC,IAAI,SAAS,GAAc;YACzB,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,SAAS;YACf,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,WAAW,EAAE,YAAY;SAC1B,CAAA;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,SAAS,CAAC,OAAO,GAAG,KAAK,CAAA;YACzB,SAAS,CAAC,WAAW,GAAG,KAAK,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAA;QAC3B,CAAC;aAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAClC,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAA;YACjC,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YAC5C,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAA;QAC5D,CAAC;aAAM,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9C,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YAC1D,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAA;YAC/E,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,QAAQ,CAAA;QACzC,CAAC;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAY;QAClC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAA;QAE3C,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7D,OAAO,SAAS,CAAA;QAClB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxE,OAAO,YAAY,CAAA;QACrB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACvE,OAAO,KAAK,CAAA;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACvE,OAAO,OAAO,CAAA;QAChB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,OAAO,SAAS,CAAA;QAClB,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAA;QAClB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,KAAU;QACvC,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAA;QAEnD,SAAS;QACT,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACpE,OAAO,gBAAgB,CAAA;QACzB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,OAAO,UAAU,CAAA;QACnB,CAAC;QAED,SAAS;aACJ,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtE,OAAO,kBAAkB,CAAA;QAC3B,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC1C,OAAO,kBAAkB,CAAA;QAC3B,CAAC;QAED,WAAW;aACN,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACzE,OAAO,gBAAgB,CAAA;QACzB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAClE,OAAO,oBAAoB,CAAA;QAC7B,CAAC;QAED,SAAS;aACJ,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACpE,OAAO,YAAY,CAAA;QACrB,CAAC;QAED,OAAO;aACF,CAAC;YACJ,OAAO,UAAU,CAAA;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,OAAe,EAAE,IAAY;QAC1C,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5C,IAAI,CAAC;gBACH,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;YACzB,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,aAAa,CAAC,CAAA;YACjE,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,OAAe;QACrB,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,EAAE,CAAC,CAAA;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,OAAe;QAClB,OAAO,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE,CAAC,CAAA;QAChC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,OAAe;QACrB,OAAO,CAAC,IAAI,CAAC,aAAa,OAAO,EAAE,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAe;QACnB,OAAO,CAAC,KAAK,CAAC,WAAW,OAAO,EAAE,CAAC,CAAA;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAC/B,CAAC;CACF;AApMD,oCAoMC"}