.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none;
}

.notification-toast {
  position: relative;
  min-width: 300px;
  max-width: 400px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  pointer-events: auto;
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.toast-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: white;
  word-wrap: break-word;
}

.toast-close {
  flex-shrink: 0;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-close:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
}

/* 成功通知樣式 */
.toast-success {
  border-left: 4px solid #10b981;
}

.toast-success .toast-icon {
  color: #10b981;
}

.toast-success .toast-progress {
  background: #10b981;
}

/* 錯誤通知樣式 */
.toast-error {
  border-left: 4px solid #ef4444;
}

.toast-error .toast-icon {
  color: #ef4444;
}

.toast-error .toast-progress {
  background: #ef4444;
}

/* 警告通知樣式 */
.toast-warning {
  border-left: 4px solid #f59e0b;
}

.toast-warning .toast-icon {
  color: #f59e0b;
}

.toast-warning .toast-progress {
  background: #f59e0b;
}

/* 信息通知樣式 */
.toast-info {
  border-left: 4px solid #3b82f6;
}

.toast-info .toast-icon {
  color: #3b82f6;
}

.toast-info .toast-progress {
  background: #3b82f6;
}

/* 響應式設計 */
@media (max-width: 480px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .notification-toast {
    min-width: auto;
    max-width: none;
  }
  
  .toast-content {
    padding: 12px;
  }
  
  .toast-message {
    font-size: 13px;
  }
}

/* 動畫效果 */
.notification-toast:hover .toast-progress {
  animation-play-state: paused;
}

/* 深色主題適配 */
@media (prefers-color-scheme: dark) {
  .notification-toast {
    background: rgba(0, 0, 0, 0.95);
    border-color: rgba(255, 255, 255, 0.15);
  }
}

/* 高對比度模式適配 */
@media (prefers-contrast: high) {
  .notification-toast {
    border-width: 2px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
  }
  
  .toast-message {
    font-weight: 500;
  }
}

/* 減少動畫模式適配 */
@media (prefers-reduced-motion: reduce) {
  .notification-toast {
    transition: none;
  }
  
  .toast-progress {
    animation: none;
  }
}
