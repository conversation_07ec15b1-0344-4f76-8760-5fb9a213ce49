/**
 * 配置管理器
 * 處理環境變數和應用程式配置
 */

const path = require('path')
const fs = require('fs')

class ConfigManager {
  constructor() {
    this.config = {}
    this.loadConfig()
  }

  /**
   * 載入配置
   */
  loadConfig() {
    try {
      // 嘗試載入 .env 檔案
      this.loadEnvFile()
      
      // 從環境變數載入配置
      this.config = {
        // Azure Speech Service 配置
        azureSpeech: {
          subscriptionKey: process.env.AZURE_SPEECH_SERVICE_API_KEY || '',
          region: process.env.AZURE_SPEECH_SERVICE_REGION || 'eastus',
          language: 'zh-TW'
        },
        
        // Azure OpenAI 配置
        azureOpenAI: {
          apiKey: process.env.AZURE_OPENAI_API_KEY || '',
          endpoint: process.env.AZURE_OPENAI_ENDPOINT || '',
          model: process.env.AZURE_OPENAI_MODEL || 'gpt-4o-mini-audio-preview'
        },
        
        // 應用程式配置
        app: {
          isDev: process.env.NODE_ENV === 'development',
          logLevel: process.env.LOG_LEVEL || 'info'
        }
      }
      
      console.log('Configuration loaded successfully')
    } catch (error) {
      console.error('Failed to load configuration:', error)
    }
  }

  /**
   * 載入 .env 檔案
   */
  loadEnvFile() {
    const envPaths = [
      path.join(process.cwd(), '.env'),
      path.join(process.cwd(), '.env.local'),
      path.join(__dirname, '../../.env'),
      path.join(__dirname, '../../.env.local')
    ]

    for (const envPath of envPaths) {
      if (fs.existsSync(envPath)) {
        console.log(`Loading environment from: ${envPath}`)
        const envContent = fs.readFileSync(envPath, 'utf8')
        this.parseEnvContent(envContent)
        break
      }
    }
  }

  /**
   * 解析 .env 檔案內容
   */
  parseEnvContent(content) {
    const lines = content.split('\n')
    
    for (const line of lines) {
      const trimmedLine = line.trim()
      
      // 跳過註釋和空行
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue
      }
      
      // 解析 KEY=VALUE 格式
      const equalIndex = trimmedLine.indexOf('=')
      if (equalIndex > 0) {
        const key = trimmedLine.substring(0, equalIndex).trim()
        const value = trimmedLine.substring(equalIndex + 1).trim()
        
        // 移除引號
        const cleanValue = value.replace(/^["']|["']$/g, '')
        process.env[key] = cleanValue
      }
    }
  }

  /**
   * 從端點 URL 中提取區域
   */
  extractRegionFromEndpoint(endpoint) {
    if (!endpoint) return null
    
    try {
      const url = new URL(endpoint)
      const hostname = url.hostname
      
      // 從 hostname 中提取區域，例如 eastus.api.cognitive.microsoft.com
      const parts = hostname.split('.')
      if (parts.length > 0) {
        return parts[0]
      }
    } catch (error) {
      console.warn('Failed to extract region from endpoint:', error)
    }
    
    return null
  }

  /**
   * 獲取配置
   */
  getConfig(path = null) {
    if (!path) {
      return this.config
    }
    
    const keys = path.split('.')
    let current = this.config
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key]
      } else {
        return null
      }
    }
    
    return current
  }

  /**
   * 設定配置值
   */
  setConfig(path, value) {
    const keys = path.split('.')
    let current = this.config
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }
    
    current[keys[keys.length - 1]] = value
  }

  /**
   * 檢查必要的配置是否存在
   */
  validateConfig() {
    const errors = []
    
    // 檢查 Azure Speech Service 配置
    if (!this.config.azureSpeech.subscriptionKey) {
      errors.push('Azure Speech Service API Key 未設定')
    }
    
    if (!this.config.azureSpeech.region) {
      errors.push('Azure Speech Service Region 未設定')
    }
    
    // 檢查 Azure OpenAI 配置
    if (!this.config.azureOpenAI.apiKey) {
      errors.push('Azure OpenAI API Key 未設定')
    }
    
    if (!this.config.azureOpenAI.endpoint) {
      errors.push('Azure OpenAI Endpoint 未設定')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 獲取 Azure Speech Service 配置
   */
  getAzureSpeechConfig() {
    return {
      subscriptionKey: this.config.azureSpeech.subscriptionKey,
      region: this.config.azureSpeech.region,
      language: this.config.azureSpeech.language
    }
  }

  /**
   * 獲取 Azure OpenAI 配置
   */
  getAzureOpenAIConfig() {
    return {
      apiKey: this.config.azureOpenAI.apiKey,
      endpoint: this.config.azureOpenAI.endpoint,
      model: this.config.azureOpenAI.model
    }
  }

  /**
   * 檢查是否為開發模式
   */
  isDevelopment() {
    return this.config.app.isDev
  }

  /**
   * 重新載入配置
   */
  reload() {
    this.loadConfig()
  }
}

module.exports = ConfigManager
