{"version": 3, "file": "ShortcutManager.js", "sourceRoot": "", "sources": ["../../../src/main/managers/ShortcutManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,uCAAyC;AAEzC,4EAAwE;AAOxE,MAAa,eAAe;IAQ1B,YAAY,aAA4B;QALhC,YAAO,GAAiB;YAC9B,MAAM,EAAE,0BAA0B;YAClC,UAAU,EAAE,0BAA0B;SACvC,CAAA;QAGC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;QAClC,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,aAAa,CAAC,CAAA;IACnE,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,aAAa,EAAE,CAAA,CAAC,YAAY;QAEjC,IAAI,CAAC;YACH,WAAW;YACX,MAAM,gBAAgB,GAAG,yBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE;gBACzE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;gBACvC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;YACjD,CAAC,CAAC,CAAA;YAEF,UAAU;YACV,MAAM,oBAAoB,GAAG,yBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,EAAE;gBACjF,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;gBAC3C,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;YACrD,CAAC,CAAC,CAAA;YAEF,IAAI,gBAAgB,IAAI,oBAAoB,EAAE,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAC3D,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAA;YACnD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAwB;QACpC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,UAAU,EAAE,CAAA;QAChC,IAAI,CAAC,iBAAiB,EAAE,CAAA;IAC1B,CAAC;IAED;;OAEG;IACH,aAAa;QACX,yBAAc,CAAC,aAAa,EAAE,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAA;IACjC,CAAC;CACF;AAtED,0CAsEC"}