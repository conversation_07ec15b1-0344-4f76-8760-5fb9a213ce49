{"version": 3, "file": "TrayManager.js", "sourceRoot": "", "sources": ["../../../src/main/managers/TrayManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;AAEH,uCAA0C;AAC1C,gDAAuB;AAGvB,MAAa,WAAW;IAItB,YAAY,aAA4B;QAHhC,SAAI,GAAgB,IAAI,CAAA;QAI9B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;IACpC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,8BAA8B;QAC9B,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAA;QACpD,MAAM,YAAY,GAAG,KAAK;YACxB,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,sBAAsB,CAAC;YAClD,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,+BAA+B,CAAC,CAAA;QACzD,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAA;QAE5C,IAAI,CAAC,IAAI,GAAG,IAAI,eAAI,CAAC,YAAY,CAAC,CAAA;QAClC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAA;QAE1C,SAAS;QACT,IAAI,CAAC,cAAc,EAAE,CAAA;QAErB,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAA;QACrC,CAAC,CAAC,CAAA;QAEF,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAA;QACrC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAM;QAEtB,MAAM,WAAW,GAAG,eAAI,CAAC,iBAAiB,CAAC;YACzC;gBACE,KAAK,EAAE,OAAO;gBACd,KAAK,EAAE,GAAG,EAAE;oBACV,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAA;gBACrC,CAAC;aACF;YACD;gBACE,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,KAAK,EAAE,wBAAwB;gBAC/B,KAAK,EAAE,GAAG,EAAE;oBACV,iBAAiB;oBACjB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;gBAC5C,CAAC;aACF;YACD;gBACE,KAAK,EAAE,wBAAwB;gBAC/B,KAAK,EAAE,GAAG,EAAE;oBACV,eAAe;oBACf,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAA;gBAChD,CAAC;aACF;YACD;gBACE,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,GAAG,EAAE;oBACV,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAA;gBACrC,CAAC;aACF;YACD;gBACE,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,GAAG,EAAE;oBACV,cAAG,CAAC,SAAS,GAAG,IAAI,CAAA;oBACpB,cAAG,CAAC,IAAI,EAAE,CAAA;gBACZ,CAAC;aACF;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;YACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;CACF;AA1GD,kCA0GC"}