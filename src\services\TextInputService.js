/**
 * 文字輸入服務
 * 處理自動文字輸入到當前焦點的應用程式
 */

const { clipboard } = require('electron')
const { keyboard, Key } = require('@nut-tree-fork/nut-js')

class TextInputService {
  constructor() {
    // 設定 nut.js 的配置
    keyboard.config.autoDelayMs = 10
    console.log('Text Input Service initialized with nut.js')
  }

  /**
   * 直接輸入文字（使用 nut.js）
   */
  async typeText(text) {
    try {
      console.log('Typing text directly with nut.js:', text.substring(0, 50) + '...')

      // 等待一小段時間確保焦點正確
      await this.delay(100)

      // 使用 nut.js 直接輸入文字
      await keyboard.type(text)

      console.log('Text typed successfully')
      return true
    } catch (error) {
      console.error('Direct typing failed:', error)
      console.log('Falling back to clipboard method')
      return await this.typeTextViaClipboard(text)
    }
  }

  /**
   * 使用剪貼簿輸入文字（備用方案）
   */
  async typeTextViaClipboard(text) {
    try {
      console.log('Typing text via clipboard:', text.substring(0, 50) + '...')
      
      // 保存當前剪貼簿內容
      const originalClipboard = clipboard.readText()
      
      // 將文字複製到剪貼簿
      clipboard.writeText(text)
      
      // 等待一小段時間
      await this.delay(100)
      
      // 使用 nut.js 模擬 Ctrl+V 貼上
      await keyboard.pressKey(Key.LeftControl, Key.V)
      await keyboard.releaseKey(Key.LeftControl, Key.V)
      
      // 等待貼上完成後恢復原剪貼簿內容
      setTimeout(() => {
        clipboard.writeText(originalClipboard)
      }, 1000)
      
      console.log('Text pasted successfully')
      return true
    } catch (error) {
      console.error('Clipboard typing failed:', error)
      return false
    }
  }

  /**
   * 智能文字輸入（自動選擇最佳方案）
   */
  async smartTypeText(text) {
    try {
      // 檢查文字長度和內容
      const isLongText = text.length > 100
      const hasSpecialChars = /[^\x00-\x7F]/.test(text) // 檢查是否有非 ASCII 字符
      
      // 對於長文字或包含特殊字符的文字，優先使用剪貼簿方案
      if (isLongText || hasSpecialChars) {
        console.log('Using clipboard method for long/special text')
        const success = await this.typeTextViaClipboard(text)
        if (success) return true
        
        // 如果剪貼簿方案失敗，嘗試直接輸入
        console.log('Clipboard method failed, trying direct typing')
        return await this.typeText(text)
      } else {
        // 對於短文字，優先使用直接輸入
        console.log('Using direct typing for short text')
        const success = await this.typeText(text)
        if (success) return true
        
        // 如果直接輸入失敗，嘗試剪貼簿方案
        console.log('Direct typing failed, trying clipboard method')
        return await this.typeTextViaClipboard(text)
      }
    } catch (error) {
      console.error('Smart typing failed:', error)
      return false
    }
  }

  /**
   * 模擬按鍵組合
   */
  async pressKeys(key, modifiers = []) {
    try {
      const keys = []

      // 轉換修飾鍵
      for (const modifier of modifiers) {
        switch (modifier.toLowerCase()) {
          case 'control':
          case 'ctrl':
            keys.push(Key.LeftControl)
            break
          case 'shift':
            keys.push(Key.LeftShift)
            break
          case 'alt':
            keys.push(Key.LeftAlt)
            break
          case 'meta':
          case 'cmd':
            keys.push(Key.LeftCmd)
            break
        }
      }

      // 添加主鍵
      const mainKey = Key[key.toUpperCase()] || key
      keys.push(mainKey)

      // 按下所有鍵
      await keyboard.pressKey(...keys)
      await keyboard.releaseKey(...keys)

      return true
    } catch (error) {
      console.error('Key press failed:', error)
      return false
    }
  }

  /**
   * 獲取當前鼠標位置
   */
  async getMousePosition() {
    try {
      const { mouse } = require('@nut-tree-fork/nut-js')
      const position = await mouse.getPosition()
      return { x: position.x, y: position.y }
    } catch (error) {
      console.error('Failed to get mouse position:', error)
      return { x: 0, y: 0 }
    }
  }

  /**
   * 點擊指定位置
   */
  async clickAt(x, y) {
    try {
      const { mouse, Button } = require('@nut-tree-fork/nut-js')
      await mouse.setPosition({ x, y })
      await mouse.click(Button.LEFT)
      return true
    } catch (error) {
      console.error('Click failed:', error)
      return false
    }
  }

  /**
   * 清除當前輸入欄位的內容
   */
  async clearCurrentField() {
    try {
      // 全選當前內容 (Ctrl+A)
      await keyboard.pressKey(Key.LeftControl, Key.A)
      await keyboard.releaseKey(Key.LeftControl, Key.A)
      await this.delay(50)

      // 刪除選中的內容
      await keyboard.pressKey(Key.Delete)
      await keyboard.releaseKey(Key.Delete)
      await this.delay(50)

      return true
    } catch (error) {
      console.error('Clear field failed:', error)
      return false
    }
  }

  /**
   * 在當前位置插入文字（不清除現有內容）
   */
  async insertText(text) {
    return await this.smartTypeText(text)
  }

  /**
   * 替換當前欄位的所有內容
   */
  async replaceText(text) {
    try {
      // 先清除當前內容
      await this.clearCurrentField()
      
      // 然後輸入新文字
      return await this.smartTypeText(text)
    } catch (error) {
      console.error('Replace text failed:', error)
      return false
    }
  }

  /**
   * 檢查是否有活動的文字輸入欄位
   */
  hasActiveTextField() {
    try {
      // 嘗試獲取當前焦點元素（這是一個簡化的檢查）
      // 在實際應用中，可能需要更複雜的邏輯來檢測
      return true // 暫時總是返回 true
    } catch (error) {
      console.error('Failed to check active text field:', error)
      return false
    }
  }

  /**
   * 延遲函數
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 測試文字輸入功能
   */
  async testTextInput() {
    try {
      const testText = '測試文字輸入功能'
      console.log('Testing text input with:', testText)
      
      const success = await this.smartTypeText(testText)
      
      if (success) {
        console.log('Text input test passed')
      } else {
        console.log('Text input test failed')
      }
      
      return success
    } catch (error) {
      console.error('Text input test error:', error)
      return false
    }
  }

  /**
   * 獲取螢幕尺寸
   */
  async getScreenSize() {
    try {
      const { screen } = require('@nut-tree-fork/nut-js')
      const size = await screen.size()
      return { width: size.width, height: size.height }
    } catch (error) {
      console.error('Failed to get screen size:', error)
      return { width: 1920, height: 1080 }
    }
  }

  /**
   * 釋放資源
   */
  dispose() {
    console.log('Text Input Service disposed')
  }
}

module.exports = TextInputService
