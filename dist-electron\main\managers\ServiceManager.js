"use strict";
/**
 * 服務管理器
 * 負責初始化和管理所有服務
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceManager = void 0;
const ConfigManager_1 = require("../../services/ConfigManager");
const AzureSpeechService_1 = require("../../services/AzureSpeechService");
const AzureOpenAIService_1 = require("../../services/AzureOpenAIService");
const TextInputService_1 = require("../../services/TextInputService");
const ErrorHandler_1 = require("../../services/ErrorHandler");
class ServiceManager {
    constructor() {
        this.speechService = null;
        this.openAIService = null;
        this.textInputService = null;
        this.configManager = new ConfigManager_1.ConfigManager();
        this.errorHandler = new ErrorHandler_1.ErrorHandler();
    }
    /**
     * 初始化所有服務
     */
    async initialize() {
        console.log('Initializing services...');
        try {
            // 初始化語音服務
            await this.initializeSpeechService();
            // 初始化 OpenAI 服務
            await this.initializeOpenAIService();
            // 初始化文字輸入服務
            await this.initializeTextInputService();
            console.log('All services initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize services:', error);
            throw error;
        }
    }
    /**
     * 初始化語音服務
     */
    async initializeSpeechService() {
        try {
            const config = this.configManager.getAzureSpeechConfig();
            if (config.subscriptionKey && config.region) {
                this.speechService = new AzureSpeechService_1.AzureSpeechService({
                    subscriptionKey: config.subscriptionKey,
                    region: config.region,
                    language: config.language
                });
                console.log('Azure Speech Service initialized successfully');
                console.log('Azure Speech Service 初始化成功');
            }
            else {
                console.warn('Azure Speech Service credentials not found, service not initialized');
            }
        }
        catch (error) {
            console.error('Failed to initialize Azure Speech Service:', error);
            this.errorHandler.handleSpeechError(error);
        }
    }
    /**
     * 初始化 OpenAI 服務
     */
    async initializeOpenAIService() {
        try {
            const config = this.configManager.getAzureOpenAIConfig();
            if (config.apiKey && config.endpoint) {
                this.openAIService = new AzureOpenAIService_1.AzureOpenAIService({
                    apiKey: config.apiKey,
                    endpoint: config.endpoint,
                    model: config.model
                });
                console.log('Azure OpenAI Service initialized successfully');
                console.log('Azure OpenAI Service 初始化成功');
            }
            else {
                console.warn('Azure OpenAI Service credentials not found, service not initialized');
            }
        }
        catch (error) {
            console.error('Failed to initialize Azure OpenAI Service:', error);
            this.errorHandler.handleOpenAIError(error);
        }
    }
    /**
     * 初始化文字輸入服務
     */
    async initializeTextInputService() {
        try {
            this.textInputService = new TextInputService_1.TextInputService();
            console.log('Text Input Service initialized with nut.js');
            console.log('Text Input Service 初始化成功');
        }
        catch (error) {
            console.error('Failed to initialize Text Input Service:', error);
            this.errorHandler.handleTextInputError(error);
        }
    }
    /**
     * 獲取語音服務
     */
    getSpeechService() {
        return this.speechService;
    }
    /**
     * 獲取 OpenAI 服務
     */
    getOpenAIService() {
        return this.openAIService;
    }
    /**
     * 獲取文字輸入服務
     */
    getTextInputService() {
        return this.textInputService;
    }
    /**
     * 獲取錯誤處理器
     */
    getErrorHandler() {
        return this.errorHandler;
    }
    /**
     * 獲取配置管理器
     */
    getConfigManager() {
        return this.configManager;
    }
    /**
     * 更新 API 配置
     */
    async updateApiConfig(apiConfig) {
        try {
            console.log('Updating API configuration');
            // 更新配置
            this.configManager.updateConfig(apiConfig);
            // 重新初始化相關服務
            if (apiConfig.azureSpeech) {
                await this.initializeSpeechService();
            }
            if (apiConfig.azureOpenAI) {
                await this.initializeOpenAIService();
            }
            console.log('API configuration updated successfully');
        }
        catch (error) {
            console.error('Failed to update API configuration:', error);
            throw error;
        }
    }
    /**
     * 關閉所有服務
     */
    async shutdown() {
        console.log('Shutting down services...');
        // 這裡可以添加服務清理邏輯
        this.speechService = null;
        this.openAIService = null;
        this.textInputService = null;
        console.log('Services shutdown complete');
    }
}
exports.ServiceManager = ServiceManager;
//# sourceMappingURL=ServiceManager.js.map