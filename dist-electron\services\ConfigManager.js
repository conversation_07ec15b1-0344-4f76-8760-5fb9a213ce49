"use strict";
/**
 * 配置管理器
 * 處理環境變數和應用程式配置
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
class ConfigManager {
    constructor() {
        this.config = this.getDefaultConfig();
        this.loadConfig();
    }
    /**
     * 獲取預設配置
     */
    getDefaultConfig() {
        return {
            azureSpeech: {
                subscriptionKey: '',
                region: 'eastus',
                endpoint: '',
                language: 'zh-TW'
            },
            azureOpenAI: {
                apiKey: '',
                endpoint: '',
                model: 'gpt-4o-mini-audio-preview'
            },
            app: {
                isDev: process.env.NODE_ENV === 'development',
                logLevel: 'info'
            }
        };
    }
    /**
     * 載入配置
     */
    loadConfig() {
        try {
            // 嘗試載入 .env 檔案
            this.loadEnvFile();
            // 從環境變數載入配置
            this.config = {
                // Azure Speech Service 配置
                azureSpeech: {
                    subscriptionKey: process.env.AZURE_SPEECH_SERVICE_API_KEY || '',
                    region: this.extractRegionFromEndpoint(process.env.AZURE_SPEECH_SERVICE_ENDPOINT) || 'eastus',
                    endpoint: process.env.AZURE_SPEECH_SERVICE_ENDPOINT || '',
                    language: 'zh-TW'
                },
                // Azure OpenAI 配置
                azureOpenAI: {
                    apiKey: process.env.AZURE_OPENAI_API_KEY || '',
                    endpoint: process.env.AZURE_OPENAI_ENDPOINT || '',
                    model: process.env.AZURE_OPENAI_MODEL || 'gpt-4o-mini-audio-preview'
                },
                // 應用程式配置
                app: {
                    isDev: process.env.NODE_ENV === 'development',
                    logLevel: process.env.LOG_LEVEL || 'info'
                }
            };
            console.log('Configuration loaded successfully');
        }
        catch (error) {
            console.error('Failed to load configuration:', error);
        }
    }
    /**
     * 載入 .env 檔案
     */
    loadEnvFile() {
        const envPath = path_1.default.join(process.cwd(), '.env.local');
        console.log('Loading environment from:', envPath);
        if (fs_1.default.existsSync(envPath)) {
            const envContent = fs_1.default.readFileSync(envPath, 'utf8');
            const lines = envContent.split('\n');
            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (trimmedLine && !trimmedLine.startsWith('#')) {
                    const [key, ...valueParts] = trimmedLine.split('=');
                    if (key && valueParts.length > 0) {
                        const value = valueParts.join('=').replace(/^["']|["']$/g, '');
                        process.env[key.trim()] = value;
                    }
                }
            });
        }
    }
    /**
     * 從端點 URL 提取區域
     */
    extractRegionFromEndpoint(endpoint) {
        if (!endpoint)
            return null;
        try {
            const url = new URL(endpoint);
            const hostname = url.hostname;
            // Azure Speech Service 端點格式: https://<region>.api.cognitive.microsoft.com
            const match = hostname.match(/^([^.]+)\.api\.cognitive\.microsoft\.com$/);
            return match ? match[1] : null;
        }
        catch (error) {
            console.warn('Failed to extract region from endpoint:', endpoint);
            return null;
        }
    }
    /**
     * 獲取完整配置
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * 獲取 Azure Speech Service 配置
     */
    getAzureSpeechConfig() {
        return { ...this.config.azureSpeech };
    }
    /**
     * 獲取 Azure OpenAI 配置
     */
    getAzureOpenAIConfig() {
        return { ...this.config.azureOpenAI };
    }
    /**
     * 獲取應用程式配置
     */
    getAppConfig() {
        return { ...this.config.app };
    }
    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
    }
    /**
     * 驗證 Azure Speech Service 配置
     */
    validateAzureSpeechConfig() {
        const config = this.config.azureSpeech;
        return !!(config.subscriptionKey && config.region);
    }
    /**
     * 驗證 Azure OpenAI 配置
     */
    validateAzureOpenAIConfig() {
        const config = this.config.azureOpenAI;
        return !!(config.apiKey && config.endpoint);
    }
    /**
     * 獲取配置狀態
     */
    getConfigStatus() {
        return {
            azureSpeech: this.validateAzureSpeechConfig(),
            azureOpenAI: this.validateAzureOpenAIConfig()
        };
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=ConfigManager.js.map