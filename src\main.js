const { app, BrowserWindow, globalShortcut, Tray, Menu, ipcMain, clipboard } = require('electron')
const path = require('path')
const ConfigManager = require('./services/ConfigManager')
const AzureSpeechService = require('./services/AzureSpeechService')
const AzureOpenAIService = require('./services/AzureOpenAIService')
const TextInputService = require('./services/TextInputService')
const errorHandler = require('./services/ErrorHandler')

const isDev = process.env.NODE_ENV === 'development'
const configManager = new ConfigManager()
let speechService = null
let openAIService = null
let textInputService = null

let mainWindow = null
let recordingWindow = null
let tray = null
let isRecording = false
let currentMode = null

// 設定預設快捷鍵
let hotkeys = {
  aiMode: 'CommandOrControl+Shift+C',
  directMode: 'CommandOrControl+Shift+V'
}

// 初始化 Azure Speech Service
function initializeSpeechService() {
  try {
    const speechConfig = configManager.getAzureSpeechConfig()

    if (!speechConfig.subscriptionKey || !speechConfig.region) {
      console.warn('Azure Speech Service 配置不完整，將使用模擬模式')
      return false
    }

    speechService = new AzureSpeechService(speechConfig)
    console.log('Azure Speech Service 初始化成功')
    return true
  } catch (error) {
    console.error('Azure Speech Service 初始化失敗:', error)
    return false
  }
}

// 初始化 Azure OpenAI Service
function initializeOpenAIService() {
  try {
    const openAIConfig = configManager.getAzureOpenAIConfig()

    if (!openAIConfig.apiKey || !openAIConfig.endpoint) {
      console.warn('Azure OpenAI 配置不完整，將使用模擬模式')
      return false
    }

    openAIService = new AzureOpenAIService(openAIConfig)
    console.log('Azure OpenAI Service 初始化成功')
    return true
  } catch (error) {
    console.error('Azure OpenAI Service 初始化失敗:', error)
    return false
  }
}

// 初始化文字輸入服務
function initializeTextInputService() {
  try {
    textInputService = new TextInputService()
    console.log('Text Input Service 初始化成功')
    return true
  } catch (error) {
    console.error('Text Input Service 初始化失敗:', error)
    return false
  }
}

function createMainWindow() {
  console.log('Creating main window, isDev:', isDev)

  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    show: false, // 初始隱藏主視窗
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    icon: path.join(__dirname, '../assets/tray-icon.png')
  })

  if (isDev) {
    console.log('Loading main window from dev server')
    mainWindow.loadURL('http://localhost:5173')
    mainWindow.webContents.openDevTools()
  } else {
    console.log('Loading main window from dist')
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // 在開發模式下，主視窗預設顯示以便調試
  if (isDev) {
    mainWindow.show()
  }

  mainWindow.on('close', (event) => {
    if (!app.isQuiting) {
      event.preventDefault()
      mainWindow.hide()
    }
  })
}

function createRecordingWindow() {
  console.log('Creating recording window, isDev:', isDev)

  recordingWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    resizable: false,
    transparent: true,
    show: false, // 初始隱藏，等待內容載入完成
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    }
  })

  if (isDev) {
    console.log('Loading recording window from dev server')
    recordingWindow.loadURL('http://localhost:5173')
  } else {
    console.log('Loading recording window from dist')
    recordingWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // 居中顯示
  recordingWindow.center()

  recordingWindow.on('closed', () => {
    recordingWindow = null
    isRecording = false
    currentMode = null
  })

  // 確保錄音視窗內容載入完成後再顯示
  recordingWindow.webContents.once('did-finish-load', () => {
    console.log('Recording window content loaded')
  })
}

function createTray() {
  const trayIconPath = path.join(__dirname, '../assets/tray-icon.png')
  console.log('Tray icon path:', trayIconPath)
  tray = new Tray(trayIconPath)

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'SpeechPilot',
      type: 'normal',
      enabled: false
    },
    {
      type: 'separator'
    },
    {
      label: '顯示主視窗',
      click: () => {
        mainWindow.show()
        mainWindow.focus()
      }
    },
    {
      label: '設定',
      click: () => {
        mainWindow.show()
        mainWindow.focus()
        mainWindow.webContents.send('show-settings')
      }
    },
    {
      type: 'separator'
    },
    {
      label: '開始 AI 語音助手',
      click: () => {
        handleVoiceInput('ai')
      }
    },
    {
      label: '開始直接語音轉文字',
      click: () => {
        handleVoiceInput('direct')
      }
    },
    {
      type: 'separator'
    },
    {
      label: `AI 模式快捷鍵: ${hotkeys.aiMode}`,
      enabled: false
    },
    {
      label: `直接模式快捷鍵: ${hotkeys.directMode}`,
      enabled: false
    },
    {
      type: 'separator'
    },
    {
      label: '退出',
      click: () => {
        app.isQuiting = true
        app.quit()
      }
    }
  ])

  tray.setContextMenu(contextMenu)
  tray.setToolTip('SpeechPilot - AI語音助手')

  tray.on('double-click', () => {
    mainWindow.show()
    mainWindow.focus()
  })
}

function registerGlobalShortcuts() {
  // 清除現有快捷鍵
  globalShortcut.unregisterAll()

  // 註冊 AI 模式快捷鍵
  globalShortcut.register(hotkeys.aiMode, () => {
    handleVoiceInput('ai')
  })

  // 註冊直接模式快捷鍵
  globalShortcut.register(hotkeys.directMode, () => {
    handleVoiceInput('direct')
  })

  console.log('Global shortcuts registered:', hotkeys)
}

function handleVoiceInput(mode) {
  if (isRecording) {
    // 如果正在錄音，停止錄音
    stopRecording()
  } else {
    // 開始錄音
    startRecording(mode)
  }
}

function startRecording(mode) {
  isRecording = true
  currentMode = mode

  // 創建錄音視窗
  if (!recordingWindow || recordingWindow.isDestroyed()) {
    createRecordingWindow()
  }

  // 確保錄音視窗內容載入完成後再顯示和發送訊息
  if (recordingWindow.webContents.isLoading()) {
    recordingWindow.webContents.once('did-finish-load', () => {
      recordingWindow.show()
      recordingWindow.focus()
      recordingWindow.webContents.send('start-recording', { mode })
      console.log(`Started recording in ${mode} mode`)
    })
  } else {
    recordingWindow.show()
    recordingWindow.focus()
    recordingWindow.webContents.send('start-recording', { mode })
    console.log(`Started recording in ${mode} mode`)
  }
}

function stopRecording() {
  if (!isRecording) return

  // 注意：不要在這裡設置 isRecording = false
  // 讓錄音組件處理停止邏輯，並等待處理完成

  // 發送停止錄音訊息
  if (recordingWindow && !recordingWindow.isDestroyed()) {
    recordingWindow.webContents.send('stop-recording')
  }

  console.log('Stopping recording, waiting for audio processing...')
}

async function processVoiceInput() {
  try {
    // 這裡會整合實際的語音處理邏輯
    let resultText = ''

    if (currentMode === 'ai') {
      // AI 模式 - 整合 Azure OpenAI
      resultText = await processWithAI()
    } else {
      // 直接模式 - 整合 Azure Speech Service
      resultText = await processWithSpeech()
    }

    // 將結果輸入到當前焦點的應用程式
    await typeText(resultText)

    // 關閉錄音視窗
    if (recordingWindow) {
      recordingWindow.close()
    }

  } catch (error) {
    console.error('Error processing voice input:', error)

    if (recordingWindow) {
      recordingWindow.close()
    }
  }

  currentMode = null
}

async function processAudioData(audioData, mode) {
  try {
    let resultText = ''

    if (mode === 'ai') {
      // AI 模式 - 整合 Azure OpenAI
      resultText = await processWithAI(audioData)
    } else {
      // 直接模式 - 整合 Azure Speech Service
      resultText = await processWithSpeech(audioData)
    }

    // 發送處理完成訊息到錄音視窗
    if (recordingWindow && !recordingWindow.isDestroyed()) {
      recordingWindow.webContents.send('processing-complete', {
        text: resultText
      })
    }

    // 將結果輸入到當前焦點的應用程式
    await typeText(resultText)

    // 延遲關閉錄音視窗，讓用戶看到完成訊息
    setTimeout(() => {
      if (recordingWindow && !recordingWindow.isDestroyed()) {
        recordingWindow.close()
      }
      // 重置錄音狀態
      isRecording = false
      currentMode = null
    }, 1500)

  } catch (error) {
    console.error('Error processing audio data:', error)

    // 發送錯誤訊息到錄音視窗
    if (recordingWindow && !recordingWindow.isDestroyed()) {
      recordingWindow.webContents.send('processing-error', {
        message: '處理音頻時發生錯誤，請重試'
      })
    }

    // 重置錄音狀態
    isRecording = false
    currentMode = null
    throw error
  }
}

async function processWithAI(audioData = null) {
  try {
    if (!audioData) {
      const error = new Error('沒有音頻數據')
      errorHandler.handleAIError(error)
      throw error
    }

    if (!openAIService) {
      const warning = 'OpenAI Service 未初始化，使用模擬回應'
      console.warn(warning)
      errorHandler.warning(warning)
      return '這是 AI 處理後的回應文字（模擬模式）。'
    }

    console.log('Processing audio with Azure OpenAI, data size:', audioData.length)
    errorHandler.info('正在處理 AI 回應...')

    // 使用 Azure OpenAI 進行 AI 處理
    const result = await openAIService.processAudioWithAI(audioData)

    if (!result.text || result.text.trim() === '') {
      const error = new Error('AI 未能生成有效回應，請重試')
      errorHandler.handleAIError(error)
      throw error
    }

    console.log('AI processing result:', result)
    errorHandler.success('AI 處理完成')
    return result.text

  } catch (error) {
    const errorInfo = errorHandler.handleAIError(error)
    throw new Error(errorInfo.userMessage)
  }
}

async function processWithSpeech(audioData = null) {
  try {
    if (!audioData) {
      const error = new Error('沒有音頻數據')
      errorHandler.handleSpeechError(error)
      throw error
    }

    if (!speechService) {
      const warning = 'Speech Service 未初始化，使用模擬回應'
      console.warn(warning)
      errorHandler.warning(warning)
      return '這是語音轉換的文字（模擬模式）。'
    }

    console.log('Processing audio with Azure Speech Service, data size:', audioData.length)
    errorHandler.info('正在處理語音識別...')

    // 使用 Azure Speech Service 進行語音識別
    const result = await speechService.recognizeFromBase64(audioData)

    if (!result.text || result.text.trim() === '') {
      const error = new Error('無法識別語音內容，請重新錄音')
      errorHandler.handleSpeechError(error)
      throw error
    }

    console.log('Speech recognition result:', result)
    errorHandler.success('語音識別完成')
    return result.text

  } catch (error) {
    const errorInfo = errorHandler.handleSpeechError(error)
    throw new Error(errorInfo.userMessage)
  }
}

async function typeText(text) {
  try {
    if (!textInputService) {
      const warning = 'Text Input Service 未初始化，使用剪貼簿備用方案'
      console.warn(warning)
      errorHandler.warning(warning)

      // 備用方案：使用剪貼簿
      const previousClipboard = clipboard.readText()
      clipboard.writeText(text)
      console.log('Text copied to clipboard:', text.substring(0, 50) + '...')
      errorHandler.info('文字已複製到剪貼簿，請手動貼上 (Ctrl+V)')

      // 恢復原來的剪貼簿內容
      setTimeout(() => {
        clipboard.writeText(previousClipboard)
      }, 2000)
      return
    }

    console.log('Typing text using Text Input Service:', text.substring(0, 50) + '...')
    errorHandler.info('正在自動輸入文字...')

    // 等待一小段時間確保錄音視窗已關閉，焦點回到原應用程式
    await new Promise(resolve => setTimeout(resolve, 500))

    // 使用智能文字輸入
    const success = await textInputService.smartTypeText(text)

    if (success) {
      console.log('Text input successful')
      errorHandler.success('文字輸入完成')
    } else {
      console.error('Text input failed, falling back to clipboard')
      errorHandler.warning('自動輸入失敗，已複製到剪貼簿')

      // 備用方案
      const previousClipboard = clipboard.readText()
      clipboard.writeText(text)
      setTimeout(() => {
        clipboard.writeText(previousClipboard)
      }, 2000)
    }
  } catch (error) {
    errorHandler.handleTextInputError(error)

    // 最後的備用方案
    try {
      const previousClipboard = clipboard.readText()
      clipboard.writeText(text)
      errorHandler.info('文字已複製到剪貼簿作為備用方案')
      setTimeout(() => {
        clipboard.writeText(previousClipboard)
      }, 2000)
    } catch (clipboardError) {
      errorHandler.error('文字輸入和剪貼簿操作都失敗了')
    }
  }
}

// IPC 事件處理
ipcMain.on('update-hotkeys', (event, newHotkeys) => {
  hotkeys = newHotkeys
  registerGlobalShortcuts()

  // 更新托盤選單
  createTray()
})

// 處理按鈕點擊開始錄音請求
ipcMain.on('start-recording-request', (event, data) => {
  const { mode } = data
  console.log('Received start recording request from button click:', mode)
  handleVoiceInput(mode)
})

// 處理錄音完成事件
ipcMain.on('recording-completed', async (event, data) => {
  try {
    const { audioData, mode } = data
    console.log('Received audio data for processing, mode:', mode)

    // 處理音頻數據
    await processAudioData(audioData, mode)

  } catch (error) {
    console.error('Error in recording-completed handler:', error)
    // processAudioData 已經處理了錯誤訊息發送，這裡不需要重複處理
  }
})

// 處理錄音取消事件
ipcMain.on('recording-cancelled', (event) => {
  console.log('Recording cancelled by user')
  isRecording = false
  currentMode = null

  if (recordingWindow) {
    recordingWindow.close()
  }
})

// 處理 API 配置更新事件
ipcMain.on('update-api-config', (event, apiConfig) => {
  try {
    console.log('Updating API configuration')

    // 更新配置管理器
    if (apiConfig.azureSpeechKey) {
      configManager.setConfig('azureSpeech.subscriptionKey', apiConfig.azureSpeechKey)
    }
    if (apiConfig.azureSpeechRegion) {
      configManager.setConfig('azureSpeech.region', apiConfig.azureSpeechRegion)
    }
    if (apiConfig.azureOpenAIKey) {
      configManager.setConfig('azureOpenAI.apiKey', apiConfig.azureOpenAIKey)
    }
    if (apiConfig.azureOpenAIEndpoint) {
      configManager.setConfig('azureOpenAI.endpoint', apiConfig.azureOpenAIEndpoint)
    }

    // 重新初始化服務
    if (apiConfig.azureSpeechKey || apiConfig.azureSpeechRegion) {
      initializeSpeechService()
    }

    if (apiConfig.azureOpenAIKey || apiConfig.azureOpenAIEndpoint) {
      initializeOpenAIService()
    }

    console.log('API configuration updated successfully')
  } catch (error) {
    console.error('Failed to update API configuration:', error)
  }
})

app.whenReady().then(() => {
  // 初始化服務
  initializeSpeechService()
  initializeOpenAIService()
  initializeTextInputService()

  createMainWindow()
  createTray() // 啟用托盤功能
  registerGlobalShortcuts()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('will-quit', () => {
  globalShortcut.unregisterAll()
})

app.on('before-quit', () => {
  app.isQuiting = true
})
