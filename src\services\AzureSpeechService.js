/**
 * Azure Speech Service 整合 (JavaScript 版本)
 * 處理語音轉文字功能
 */

const sdk = require('microsoft-cognitiveservices-speech-sdk')

class AzureSpeechService {
  constructor(config) {
    this.speechConfig = null
    this.audioConfig = null
    this.initializeConfig(config)
  }

  /**
   * 初始化 Speech Service 配置
   */
  initializeConfig(config) {
    try {
      this.speechConfig = sdk.SpeechConfig.fromSubscription(
        config.subscriptionKey,
        config.region
      )
      
      // 設定語言，預設為繁體中文
      this.speechConfig.speechRecognitionLanguage = config.language || 'zh-TW'
      
      // 設定輸出格式
      this.speechConfig.outputFormat = sdk.OutputFormat.Detailed
      
      console.log('Azure Speech Service initialized successfully')
    } catch (error) {
      console.error('Failed to initialize Azure Speech Service:', error)
      throw new Error('Azure Speech Service 初始化失敗')
    }
  }

  /**
   * 從音頻 Base64 字串進行語音識別
   */
  async recognizeFromBase64(audioBase64) {
    if (!this.speechConfig) {
      throw new Error('Speech Service 未初始化')
    }

    return new Promise((resolve, reject) => {
      try {
        // 將 Base64 轉換為 Buffer
        const audioBuffer = Buffer.from(audioBase64, 'base64')
        
        // 創建音頻配置
        const audioConfig = sdk.AudioConfig.fromWavFileInput(audioBuffer)
        
        // 創建語音識別器
        const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig)
        
        const startTime = Date.now()
        
        // 設定事件處理器
        recognizer.recognizeOnceAsync(
          (result) => {
            const duration = Date.now() - startTime
            
            if (result.reason === sdk.ResultReason.RecognizedSpeech) {
              const speechResult = {
                text: result.text,
                confidence: this.extractConfidence(result),
                duration
              }
              
              console.log('Speech recognition successful:', speechResult)
              resolve(speechResult)
            } else if (result.reason === sdk.ResultReason.NoMatch) {
              reject(new Error('無法識別語音內容，請重試'))
            } else {
              reject(new Error(`語音識別失敗: ${result.errorDetails}`))
            }
            
            recognizer.close()
          },
          (error) => {
            console.error('Speech recognition error:', error)
            recognizer.close()
            reject(new Error(`語音識別錯誤: ${error}`))
          }
        )
      } catch (error) {
        console.error('Error in recognizeFromBase64:', error)
        reject(error)
      }
    })
  }

  /**
   * 連續語音識別（用於即時轉錄）
   */
  async startContinuousRecognition(onResult, onError) {
    if (!this.speechConfig) {
      throw new Error('Speech Service 未初始化')
    }

    // 使用預設麥克風
    const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput()
    const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig)

    // 設定事件處理器
    recognizer.recognizing = (s, e) => {
      console.log(`Recognizing: ${e.result.text}`)
    }

    recognizer.recognized = (s, e) => {
      if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
        onResult(e.result.text)
      }
    }

    recognizer.canceled = (s, e) => {
      console.log(`Recognition canceled: ${e.reason}`)
      if (e.reason === sdk.CancellationReason.Error) {
        onError(`錯誤: ${e.errorDetails}`)
      }
      recognizer.stopContinuousRecognitionAsync()
    }

    recognizer.sessionStopped = (s, e) => {
      console.log('Recognition session stopped')
      recognizer.stopContinuousRecognitionAsync()
    }

    // 開始連續識別
    recognizer.startContinuousRecognitionAsync()
    
    return recognizer
  }

  /**
   * 停止連續語音識別
   */
  stopContinuousRecognition(recognizer) {
    recognizer.stopContinuousRecognitionAsync(
      () => {
        recognizer.close()
        console.log('Continuous recognition stopped')
      },
      (error) => {
        console.error('Error stopping recognition:', error)
        recognizer.close()
      }
    )
  }

  /**
   * 從識別結果中提取信心度
   */
  extractConfidence(result) {
    try {
      // 嘗試從詳細結果中提取信心度
      const detailResult = JSON.parse(result.json)
      if (detailResult.NBest && detailResult.NBest.length > 0) {
        return detailResult.NBest[0].Confidence || 0.5
      }
    } catch (error) {
      console.warn('Could not extract confidence score:', error)
    }
    return 0.5 // 預設信心度
  }

  /**
   * 測試 Speech Service 連接
   */
  async testConnection() {
    try {
      if (!this.speechConfig) {
        return false
      }

      // 創建一個簡單的測試識別器
      const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput()
      const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig)
      
      // 測試是否能創建識別器
      recognizer.close()
      return true
    } catch (error) {
      console.error('Speech Service connection test failed:', error)
      return false
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config) {
    this.initializeConfig(config)
  }

  /**
   * 釋放資源
   */
  dispose() {
    this.speechConfig = null
    this.audioConfig = null
    console.log('Azure Speech Service disposed')
  }
}

module.exports = AzureSpeechService
