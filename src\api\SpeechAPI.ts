/**
 * 可重用的語音 API 包
 * 整合 Azure Speech Service 和 Azure OpenAI Service
 * 可以輕鬆複製到其他專案使用
 */

import { AzureSpeechService, SpeechConfig } from '../services/AzureSpeechService'
import { AzureOpenAIService, AzureOpenAIConfig } from '../services/AzureOpenAIService'
import { ErrorHandler } from '../services/ErrorHandler'

export interface SpeechAPIConfig {
  azureSpeech: SpeechConfig
  azureOpenAI: AzureOpenAIConfig
}

export interface SpeechResult {
  text: string
  confidence: number
  duration: number
}

export interface AIResponse {
  text: string
  processingTime: number
}

export interface AudioProcessingOptions {
  mode: 'speech-to-text' | 'ai-assistant'
  language?: string
  maxTokens?: number
  temperature?: number
}

export class SpeechAPI {
  private speechService: AzureSpeechService | null = null
  private openAIService: AzureOpenAIService | null = null
  private errorHandler: ErrorHandler
  private isInitialized = false

  constructor(config?: SpeechAPIConfig) {
    this.errorHandler = new ErrorHandler()
    
    if (config) {
      this.initialize(config)
    }
  }

  /**
   * 初始化 API 服務
   */
  async initialize(config: SpeechAPIConfig): Promise<void> {
    try {
      console.log('Initializing Speech API...')

      // 初始化 Azure Speech Service
      if (config.azureSpeech.subscriptionKey && config.azureSpeech.region) {
        this.speechService = new AzureSpeechService(config.azureSpeech)
        console.log('Azure Speech Service initialized')
      } else {
        console.warn('Azure Speech Service credentials missing')
      }

      // 初始化 Azure OpenAI Service
      if (config.azureOpenAI.apiKey && config.azureOpenAI.endpoint) {
        this.openAIService = new AzureOpenAIService(config.azureOpenAI)
        console.log('Azure OpenAI Service initialized')
      } else {
        console.warn('Azure OpenAI Service credentials missing')
      }

      this.isInitialized = true
      console.log('Speech API initialized successfully')
    } catch (error) {
      console.error('Failed to initialize Speech API:', error)
      throw error
    }
  }

  /**
   * 處理音頻數據 - 主要入口點
   */
  async processAudio(
    audioData: string | Blob, 
    options: AudioProcessingOptions = { mode: 'speech-to-text' }
  ): Promise<SpeechResult | AIResponse> {
    if (!this.isInitialized) {
      throw new Error('Speech API not initialized. Call initialize() first.')
    }

    const startTime = Date.now()

    try {
      if (options.mode === 'speech-to-text') {
        return await this.speechToText(audioData)
      } else if (options.mode === 'ai-assistant') {
        return await this.aiAssistant(audioData, options)
      } else {
        throw new Error(`Unsupported mode: ${options.mode}`)
      }
    } catch (error) {
      console.error('Error processing audio:', error)
      this.errorHandler.handleError(error, 'AudioProcessing')
      throw error
    }
  }

  /**
   * 語音轉文字
   */
  async speechToText(audioData: string | Blob): Promise<SpeechResult> {
    if (!this.speechService) {
      throw new Error('Azure Speech Service not initialized')
    }

    try {
      console.log('Processing speech to text...')
      
      let result
      if (typeof audioData === 'string') {
        // Base64 格式
        result = await this.speechService.recognizeFromBase64(audioData)
      } else {
        // Blob 格式
        result = await this.speechService.recognizeFromBlob(audioData)
      }

      console.log('Speech to text completed:', result.text.substring(0, 50) + '...')
      return result
    } catch (error) {
      console.error('Speech to text failed:', error)
      this.errorHandler.handleSpeechError(error)
      throw error
    }
  }

  /**
   * AI 語音助手
   */
  async aiAssistant(
    audioData: string | Blob, 
    options: AudioProcessingOptions
  ): Promise<AIResponse> {
    const startTime = Date.now()

    try {
      console.log('Processing AI assistant request...')

      // 首先進行語音識別
      const speechResult = await this.speechToText(audioData)
      
      if (!speechResult.text || speechResult.text.trim() === '') {
        throw new Error('無法識別語音內容')
      }

      // 然後使用 AI 生成回應
      if (!this.openAIService) {
        console.warn('OpenAI Service not available, returning speech text only')
        return {
          text: speechResult.text,
          processingTime: Date.now() - startTime
        }
      }

      const aiResponse = await this.openAIService.generateResponse(speechResult.text)
      
      return {
        text: aiResponse,
        processingTime: Date.now() - startTime
      }
    } catch (error) {
      console.error('AI assistant failed:', error)
      this.errorHandler.handleOpenAIError(error)
      throw error
    }
  }

  /**
   * 直接文字生成 AI 回應（不需要音頻）
   */
  async generateTextResponse(text: string, options?: {
    maxTokens?: number
    temperature?: number
  }): Promise<string> {
    if (!this.openAIService) {
      throw new Error('Azure OpenAI Service not initialized')
    }

    try {
      console.log('Generating AI response for text...')
      const response = await this.openAIService.generateResponse(text)
      console.log('AI response generated successfully')
      return response
    } catch (error) {
      console.error('Text response generation failed:', error)
      this.errorHandler.handleOpenAIError(error)
      throw error
    }
  }

  /**
   * 測試 API 連接
   */
  async testConnection(): Promise<{
    speechService: boolean
    openAIService: boolean
  }> {
    const results = {
      speechService: false,
      openAIService: false
    }

    try {
      if (this.speechService) {
        // 測試語音服務（這裡可以添加實際的測試邏輯）
        results.speechService = true
      }

      if (this.openAIService) {
        results.openAIService = await this.openAIService.testConnection()
      }
    } catch (error) {
      console.error('Connection test failed:', error)
    }

    return results
  }

  /**
   * 獲取服務狀態
   */
  getStatus(): {
    initialized: boolean
    speechServiceAvailable: boolean
    openAIServiceAvailable: boolean
  } {
    return {
      initialized: this.isInitialized,
      speechServiceAvailable: this.speechService !== null,
      openAIServiceAvailable: this.openAIService !== null
    }
  }

  /**
   * 設定錯誤處理回調
   */
  onError(callback: (error: any) => void): void {
    this.errorHandler.onError(callback)
  }

  /**
   * 設定通知回調
   */
  onNotification(callback: (message: string, type: string) => void): void {
    this.errorHandler.onNotification(callback)
  }

  /**
   * 清理資源
   */
  dispose(): void {
    this.speechService = null
    this.openAIService = null
    this.isInitialized = false
    console.log('Speech API disposed')
  }
}
