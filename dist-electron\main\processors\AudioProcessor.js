"use strict";
/**
 * 音頻處理器
 * 負責處理音頻數據和語音識別
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioProcessor = void 0;
const electron_1 = require("electron");
class AudioProcessor {
    constructor(serviceManager, windowManager) {
        this.serviceManager = serviceManager;
        this.windowManager = windowManager;
    }
    /**
     * 處理音頻數據
     */
    async processAudioData(audioData, mode) {
        try {
            let resultText = '';
            if (mode === 'ai') {
                // AI 模式 - 整合 Azure OpenAI
                resultText = await this.processWithAI(audioData);
            }
            else {
                // 直接模式 - 整合 Azure Speech Service
                resultText = await this.processWithSpeech(audioData);
            }
            // 發送處理完成訊息到錄音視窗
            this.windowManager.sendToRecordingWindow('processing-complete', {
                text: resultText
            });
            // 將結果輸入到當前焦點的應用程式
            await this.typeText(resultText);
            // 延遲關閉錄音視窗，讓用戶看到完成訊息
            setTimeout(() => {
                this.windowManager.closeRecordingWindow();
            }, 1500);
        }
        catch (error) {
            console.error('Error processing audio data:', error);
            // 發送錯誤訊息到錄音視窗
            this.windowManager.sendToRecordingWindow('processing-error', {
                message: '處理音頻時發生錯誤，請重試'
            });
            throw error;
        }
    }
    /**
     * 使用 Azure Speech Service 處理語音
     */
    async processWithSpeech(audioData) {
        const speechService = this.serviceManager.getSpeechService();
        const errorHandler = this.serviceManager.getErrorHandler();
        try {
            if (!audioData) {
                const error = new Error('沒有音頻數據');
                errorHandler.handleSpeechError(error);
                throw error;
            }
            if (!speechService) {
                const warning = 'Speech Service 未初始化，使用模擬回應';
                console.warn(warning);
                errorHandler.warning(warning);
                return '這是語音轉換的文字（模擬模式）。';
            }
            console.log('Processing audio with Azure Speech Service, data size:', audioData.length);
            errorHandler.info('正在處理語音識別...');
            // 驗證音頻數據格式
            try {
                // 檢查 Base64 格式
                const audioBuffer = Buffer.from(audioData, 'base64');
                console.log('Audio buffer size:', audioBuffer.length, 'bytes');
                if (audioBuffer.length < 1000) {
                    throw new Error('音頻數據太短，可能錄音失敗');
                }
            }
            catch (bufferError) {
                throw new Error('音頻數據格式錯誤，無法解析 Base64');
            }
            // 使用 Azure Speech Service 進行語音識別
            const result = await speechService.recognizeFromBase64(audioData);
            if (!result.text || result.text.trim() === '') {
                const error = new Error('無法識別語音內容，請確認：\n1. 說話聲音清晰\n2. 環境噪音較小\n3. 麥克風工作正常');
                errorHandler.handleSpeechError(error);
                throw error;
            }
            console.log('Speech recognition result:', result.text);
            errorHandler.success('語音識別完成');
            return result.text;
        }
        catch (error) {
            console.error('Error in processWithSpeech:', error);
            // 提供更詳細的錯誤信息
            let errorMessage = '語音識別失敗';
            const errorMsg = error instanceof Error ? error.message : String(error);
            if (errorMsg.includes('network') || errorMsg.includes('timeout')) {
                errorMessage = '網路連接問題，請檢查網路設定';
            }
            else if (errorMsg.includes('unauthorized') || errorMsg.includes('401')) {
                errorMessage = 'Azure Speech Service 認證失敗，請檢查 API 金鑰';
            }
            else if (errorMsg.includes('quota') || errorMsg.includes('429')) {
                errorMessage = 'API 使用額度已達上限，請稍後重試';
            }
            else if (errorMsg.includes('audio') || errorMsg.includes('format')) {
                errorMessage = '音頻格式不支援，請確認音頻為 WAV 格式，16kHz，16-bit，單聲道';
            }
            const enhancedError = new Error(errorMessage);
            errorHandler.handleSpeechError(enhancedError);
            throw enhancedError;
        }
    }
    /**
     * 使用 Azure OpenAI 處理語音
     */
    async processWithAI(audioData) {
        const speechService = this.serviceManager.getSpeechService();
        const openAIService = this.serviceManager.getOpenAIService();
        const errorHandler = this.serviceManager.getErrorHandler();
        try {
            // 首先進行語音識別
            const speechText = await this.processWithSpeech(audioData);
            if (!openAIService) {
                const warning = 'OpenAI Service 未初始化，返回語音識別結果';
                console.warn(warning);
                errorHandler.warning(warning);
                return speechText;
            }
            console.log('Processing with Azure OpenAI, input:', speechText.substring(0, 100) + '...');
            errorHandler.info('正在處理 AI 回應...');
            // 使用 Azure OpenAI 生成回應
            const aiResponse = await openAIService.generateResponse(speechText);
            if (!aiResponse || aiResponse.trim() === '') {
                const error = new Error('AI 服務沒有返回有效回應');
                errorHandler.handleOpenAIError(error);
                throw error;
            }
            console.log('AI response generated:', aiResponse.substring(0, 100) + '...');
            errorHandler.success('AI 回應生成完成');
            return aiResponse;
        }
        catch (error) {
            console.error('Error in processWithAI:', error);
            errorHandler.handleOpenAIError(error);
            throw error;
        }
    }
    /**
     * 將文字輸入到當前焦點的應用程式
     */
    async typeText(text) {
        const textInputService = this.serviceManager.getTextInputService();
        const errorHandler = this.serviceManager.getErrorHandler();
        try {
            if (!text || text.trim() === '') {
                console.warn('No text to type');
                return;
            }
            if (!textInputService) {
                console.warn('Text Input Service not available, copying to clipboard');
                electron_1.clipboard.writeText(text);
                errorHandler.info('文字已複製到剪貼簿');
                return;
            }
            console.log('Typing text using Text Input Service:', text.substring(0, 50) + '...');
            errorHandler.info('正在自動輸入文字...');
            // 等待一小段時間確保錄音視窗已關閉，焦點回到原應用程式
            await new Promise(resolve => setTimeout(resolve, 500));
            // 使用智能文字輸入
            const success = await textInputService.smartTypeText(text);
            if (success) {
                console.log('Text input successful');
                errorHandler.success('文字輸入完成');
            }
            else {
                console.error('Text input failed, falling back to clipboard');
                errorHandler.warning('自動輸入失敗，已複製到剪貼簿');
                // 備用方案
                const previousClipboard = electron_1.clipboard.readText();
                electron_1.clipboard.writeText(text);
                setTimeout(() => {
                    electron_1.clipboard.writeText(previousClipboard);
                }, 2000);
            }
        }
        catch (error) {
            errorHandler.handleTextInputError(error);
            // 最後的備用方案
            try {
                const previousClipboard = electron_1.clipboard.readText();
                electron_1.clipboard.writeText(text);
                errorHandler.info('文字已複製到剪貼簿作為備用方案');
                setTimeout(() => {
                    electron_1.clipboard.writeText(previousClipboard);
                }, 2000);
            }
            catch (clipboardError) {
                errorHandler.error('文字輸入和剪貼簿操作都失敗了');
            }
        }
    }
}
exports.AudioProcessor = AudioProcessor;
//# sourceMappingURL=AudioProcessor.js.map