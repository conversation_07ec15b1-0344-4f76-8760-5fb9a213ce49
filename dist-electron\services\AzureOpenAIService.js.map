{"version": 3, "file": "AzureOpenAIService.js", "sourceRoot": "", "sources": ["../../src/services/AzureOpenAIService.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;AAEH,kDAA4C;AA4B5C,MAAa,kBAAkB;IAO7B,YAAY,MAAyB;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;QAC3B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;QAC/B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,2BAA2B,CAAA;QACxD,IAAI,CAAC,UAAU,GAAG,oBAAoB,CAAA,CAAC,eAAe;QAEtD,gBAAgB;QAChB,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE;gBACP,SAAS,EAAE,IAAI,CAAC,MAAM;gBACtB,cAAc,EAAE,kBAAkB;aACnC;YACD,OAAO,EAAE,KAAK,CAAC,QAAQ;SACxB,CAAC,CAAA;QAEF,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,WAAmB,EAAE,UAAmB;QAC/D,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA;YAEjF,WAAW;YACX,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,gBAAgB,IAAI,CAAC,UAAU,EAAE,CAAA;YAE7D,SAAS;YACT,MAAM,QAAQ,GAAoB;gBAChC;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE;;;;;YAKP;iBACH;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE;wBACP;4BACE,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE;gCACL,IAAI,EAAE,WAAW;gCACjB,MAAM,EAAE,KAAK;6BACd;yBACF;qBACF;iBACF;aACF,CAAA;YAED,oBAAoB;YACpB,IAAI,UAAU,EAAE,CAAC;gBACf,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,UAAU;iBACpB,CAAC,CAAA;YACJ,CAAC;YAED,QAAQ;YACR,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,GAAG;aACjB,CAAA;YAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;YACjD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAiB,GAAG,EAAE,WAAW,CAAC,CAAA;YAEzE,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9D,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAA;gBAC3D,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;gBAC1E,OAAO,UAAU,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;YAC3C,CAAC;QAEH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;YAEpD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACxD,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBAEpD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAClC,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;gBACjC,CAAC;qBAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;gBACvC,CAAC;qBAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;gBACnC,CAAC;YACH,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,IAAY;QACjC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YAE5E,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,gBAAgB,IAAI,CAAC,UAAU,EAAE,CAAA;YAE7D,MAAM,QAAQ,GAAoB;gBAChC;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE;;;;;YAKP;iBACH;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,IAAI;iBACd;aACF,CAAA;YAED,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,GAAG;aACjB,CAAA;YAED,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAA;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAiB,GAAG,EAAE,WAAW,CAAC,CAAA;YAEzE,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9D,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAA;gBAC3D,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;gBAC/E,OAAO,UAAU,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;YAC3C,CAAC;QAEH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;YAElD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACxD,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBAEpD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAClC,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;gBACjC,CAAC;qBAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;gBACvC,CAAC;YACH,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;YACnC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;YAC/C,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;CACF;AApLD,gDAoLC"}