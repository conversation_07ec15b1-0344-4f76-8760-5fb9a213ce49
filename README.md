# SpeechPilot - AI語音助手

SpeechPilot是一個桌面應用程式，讓你可以在任何應用程式中使用語音輸入和AI指令處理。只需按下快捷鍵，說出你想要的內容，AI就會理解並自動輸入到當前的文字欄位中。 Speech in, text out.
暫時提供兩個模式:
1. AI Speech-to-Text, 用戶說話, AI理解後根據指示提供回應並自動輸入到當前的文字欄位中
- 例如: 用戶說: 幫我寫一篇100字的LINKEDIN POST關於得獎感受。AI會理解這是寫作任務，並自動生成文章並輸入到當前的文字欄位中
- gpt-4o-mini-audio-preview模型能直接進行整個流程

2. Direct Speech-to-Text, 用戶說話, AI直接把文字輸入到當前的文字欄位中
- 例如: 用戶說: 我的名字是John。AI會直接輸入 "我的名字是John" 到當前的文字欄位中
- azure speech service就足以進行整個流程

## 功能特色

- 🎤 **全域語音錄製**: 在任何應用程式中按下快捷鍵即可開始錄音，使用 Web Audio API 進行高品質音頻捕獲
- 🤖 **AI指令理解**: 整合 Azure OpenAI gpt-4o-mini-audio-preview 模型，不只是語音轉文字，還能理解複雜指令如"寫一篇100字的文章"
- 📝 **精確語音識別**: 使用 Azure Speech Service 提供高精度的語音轉文字功能
- ⌨️ **智能文字輸入**: 使用 nut.js 實現真正的自動文字輸入，支援剪貼簿備用模式
- 🔧 **完整配置管理**: 在設定面板中安全配置 API 金鑰和自定義快捷鍵
- 🚨 **智能錯誤處理**: 完整的錯誤處理機制和用戶友好的通知系統
- 🌐 **跨平台支援**: 支援Windows和macOS
- ⚡ **現代化架構**: 使用 Electron + Vite + React + TypeScript 提供快速開發體驗
- 🎨 **美觀介面**: React 組件化設計，支援 Framer Motion 動畫和互動效果

## 使用說明

### 快捷鍵操作
- **Ctrl+Shift+C**: 以AI SPEECH TO TEXT模式馬上開始錄音, 彈出開始錄音視窗, 並開始錄製音頻, AI會理解你的語音內容並根據需求生成回應後輸入到當前的文字欄位中, 再按一次快捷鍵或點擊停止錄音按鈕停止錄音
- **Ctrl+Shift+V**: 以DIRECT SPEECH TO TEXT模式馬上開始錄音, 彈出開始錄音視窗, 並開始錄製音頻,並直接把文字輸入到當前的文字欄位中, 再按一次快捷鍵或點擊停止錄音按鈕停止錄音

### 設定面板
1. 點擊主介面右上角的設定按鈕
2. 可以自定義快捷鍵組合
3. 配置 Azure API 金鑰
4. 調整其他偏好設定
5. 可調整HOTKEY按著或是按一下開始錄音

### 開始錄音視窗
- 錄音視窗會顯示錄音狀態和進度
- 錄音過程中，再次按下快捷鍵或點擊停止錄音按鈕即可停止錄音
- 錄音完成後，會顯示處理狀態和結果
- 錄音完成後，會自動關閉錄音視窗

## 技術架構

### 前端技術棧
- **Electron**: 跨平台桌面應用程式框架
- **React 18**: 用戶介面組件庫
- **TypeScript**: 類型安全的 JavaScript
- **Vite**: 快速的前端構建工具
- **Framer Motion**: 流暢的動畫庫

### 後端服務
- **Azure Speech Service**: 語音識別服務
- **Azure OpenAI**: AI 語言模型服務
- **nut.js**: 跨平台自動化庫

### 核心功能模組
- **AudioRecorder**: 音頻錄製服務
- **AzureSpeechService**: 語音識別整合
- **AzureOpenAIService**: AI 處理整合
- **TextInputService**: 自動文字輸入
- **ErrorHandler**: 錯誤處理和通知
- **ConfigManager**: 配置管理

## 安裝和設定

### 1. 環境要求
- Node.js 16+
- npm 或 yarn
- Azure Speech Service 訂閱
- Azure OpenAI 訂閱

### 2. 安裝步驟
```bash
# 克隆專案
git clone <repository-url>
cd SpeechPilot

# 安裝依賴
npm install

# 創建環境變數檔案
cp .env.example .env.local
```

### 3. 配置 API 金鑰
在 `.env.local` 檔案中設定：
```env
AZURE_SPEECH_SERVICE_API_KEY=your_speech_service_key
AZURE_SPEECH_SERVICE_ENDPOINT=https://your-region.api.cognitive.microsoft.com/
AZURE_OPENAI_API_KEY=your_openai_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/openai/deployments/gpt-4o-mini-audio-preview/chat/completions
AZURE_OPENAI_MODEL=gpt-4o-mini-audio-preview
```

### 4. 運行應用程式
```bash
# 開發模式
npm run dev

# 構建應用程式
npm run build
```